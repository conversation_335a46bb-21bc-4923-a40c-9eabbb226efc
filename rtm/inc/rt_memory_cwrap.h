/*******************************************************************************
 * COPYRIGHT (c) DERI. Nanjing, China
 *
 *        FILE NAME: rt_memory_cwrap.hpp
 *
 * FILE DESCRIPTION: rt_memory c interface wrap
 *
 * --DATE--    NAME        REVISION HISTORY
 * 20200704    liuchenxin  Original Original
 * 20220610    liuchenxin  add support for drp-tool
 *******************************************************************************/

#pragma once
#include <stddef.h>
#ifdef __cplusplus
extern "C" {
#endif

enum {
    /* signal type */
    RTMC_SIGNALIN = 1,
    R<PERSON><PERSON>_SIGNALOUT,
    R<PERSON><PERSON>_PARA,
    R<PERSON><PERSON>_CHANIN,
    RTM<PERSON>_CHANOUT,
};

enum {
    /* signal value type */
    RTMC_SIGNAL_TYPE_UINT8 = 1,
    RTMC_SIGNAL_TYPE_UINT16,
    RTMC_SIGNAL_TYPE_UINT32,
    RTMC_SIGNAL_TYPE_UINT64,
    RTMC_SIGNAL_TYPE_INT8,
    RTMC_SIGNAL_TYPE_INT16,
    RTMC_SIGNAL_TYPE_INT32,
    RTMC_SIGNAL_TYPE_INT64,
    RTMC_SIGNAL_TYPE_FLOAT,
    RTMC_SIGNAL_TYPE_DOUBLE,
    RTMC_SIGNAL_TYPE_STRING,
    RTMC_SIGNAL_TYPE_NUM,
};

/* map id */
enum {
    RTMC_MAP_SIGNALIN_VAL, // "key:signal_in_val"
    RTMC_MAP_SIGNALIN_JSON, // "key:signal_in_json"
    RTMC_MAP_SIGNALOUT_VAL, // "key:signal_out_val"
    RTMC_MAP_SIGNALOUT_JSON, // "key:signal_out_json"
    RTMC_MAP_PARA_VAL, // "key:para_val"
    RTMC_MAP_PARA_JSON, // "key:para_json"
    RTMC_MAP_QUEUEIN_JSON, // "key:queue_in_json"
    RTMC_MAP_QUEUEOUT_JSON, // "key:queue_out_json"

    RTMC_MAP_SIGNAL_MAP, // "key:signal_map"
    RTMC_MAP_PROCESS_STAGE, // "key:process_stage"
    RTMC_MAP_MAX_NUM,
};

/* map_seq id */
enum {
    RTMC_MAP_SIGNALIN_SEQ_VAL, // "key:signal_in_seq_val";
    RTMC_MAP_SIGNALIN_SEQ_JSON, // "key:signal_in_seq_json";
    RTMC_MAP_SIGNALOUT_SEQ_VAL, // "key:signal_out_seq_val";
    RTMC_MAP_SIGNALOUT_SEQ_JSON, // "key:signal_out_seq_json";
    RTMC_MAP_PARA_SEQ_VAL, // "key:para_seq_val";
    RTMC_MAP_PARA_SEQ_JSON, // "key:para_seq_json";
    RTMC_MAP_QUEUEIN_SEQ_JSON, // "key:queue_in_seq_json";
    RTMC_MAP_QUEUEOUT_SEQ_JSON, // "key:queue_out_seq_json";
    RTMC_MAP_SEQ_MAX_NUM,
};

/* vector id */
enum {
    RTMC_VEC_ANA_REF_TABLE, // "key:ana_ref_table"
    RTMC_VEC_BI_REF_TABLE, // "key:bi_ref_table"
    RTMC_VEC_SOE_REF_TABLE, // "key:soe_ref_table"
    RTMC_VEC_ALARM_REF_TABLE, // "key:alarm_ref_table"
    RTMC_VEC_TRIP_REF_TABLE, // "key:trip_ref_table"
    RTMC_VEC_ORDER_REF_TABLE, // "key:order_ref_table"
    RTMC_VEC_SYS_PARA_REF_TABLE, // "key:sys_para_ref_table"
    RTMC_VEC_DEV_PARA_REF_TABLE, // "key:dev_para_ref_table"
    RTMC_VEC_CTL_PARA_REF_TABLE, // "key:ctl_para_ref_table"
    RTMC_VEC_PROT_PARA_REF_TABLE, // "key:prot_para_ref_table"
    RTMC_VEC_COMM_PARA_REF_TABLE, // "key:comm_para_ref_table"
    RTMC_VEC_WAVE_TRIG_REF_TABLE, // "key:wave_trig_ref_table"
    RTMC_VEC_WAVE_AI_CHAN_REF_TABLE, // "key:wave_ai_chan_ref_table"
    RTMC_VEC_WAVE_DI_CHAN_REF_TABLE, // "key:wave_di_chan_ref_tablbe"
    RTMC_VEC_MAX_NUM,
    // RTMC_VEC_CUSTOM0_REF_TABLE, // "key:custom0_ref_table"
    // RTMC_VEC_CUSTOM1_REF_TABLE, // "key:custom1_ref_table"
    // RTMC_VEC_CUSTOM2_REF_TABLE, // "key:custom2_ref_table"
    // RTMC_VEC_CUSTOM3_REF_TABLE, // "key:custom3_ref_table"
    // RTMC_VEC_CUSTOM4_REF_TABLE, // "key:custom4_ref_table"
    // RTMC_KEY_SETTINGS_CONTROL, // "key:settings_control"
};

/* deque id */
enum {
    // RTMC_DEQ_TRIP_QUEUE_COMM, // "key:trip_queue_comm"
    // RTMC_DEQ_TRIP_QUEUE_WEB, // "key:trip_queue_web"
    // RTMC_DEQ_SOE_QUEUE_COMM, // "key:soe_queue_comm"
    // RTMC_DEQ_SOE_QUEUE_WEB, // "key:soe_queue_web"
    // RTMC_DEQ_OPER_QUEUE_COMM, // "key:oper_queue_comm"
    // RTMC_DEQ_OPER_QUEUE_WEB, // "key:oper_queue_web"
    RTMC_DEQ_SETTINGS_QUEUE, // "key:settings_queue"
    // additonal deque for chanout
    RTMC_DEQ_MAX_NUM,
};

/* Description: init rtmemory, invoke only once
** Parameter: void
** Return: 0: success
**         -1: failed
** use rtm_setup instead
*/
int __attribute__((deprecated)) rtm_init();

/* Description: init rtmemory, invoke only once
** Parameter: void
** Return: 0: success
**         -1: failed
*/
int rtm_setup(int reinit);

/* Description: struct of returned value
** `num`: returned size num, pointed by `s_val` and `i_val`;
** `s_val`: array of string, sizeof which is `num`;
** `i_val`: array of int, sizeof which is `num`;
** `j_val`: array of int, sizeof which is `num`;
*/
typedef struct {
    size_t num;
    char** s_val;
    int* i_val;
    int* j_val;
} RtmRet;

/* Description: free returned value. any RtmRet return by funcs should be freed.
** Parameter: pointer to RtmRet
** Return: void
*/
void rtm_free_ret(RtmRet*);

/* Description: get table id and table name pair
**              extinguished by table type(map, map_seq, vector and deque).
** Parameter: void
** Return: pointer to RtmRet
**         null: failed
**         .num: number of all tables
**         .s_val: array of table names
**         .i_val: array of table ids
*/
RtmRet* rtm_map_get_all_id();
RtmRet* rtm_map_seq_get_all_id();
RtmRet* rtm_vec_get_all_id();
RtmRet* rtm_deq_get_all_id();

/* Description: get table id if specifed table exists,
**              extinguished by table type(map, vector and deque).
** Parameter: table name string
** Return: table id
**         <=0 || >=RTM_***_MAX_NUM: not exists;
**         >0 && <RTM_***_MAX_NUM: exists.
*/
// int rtm_map_get_id(char const* name);
// int rtm_vec_get_id(char const* name);
// int rtm_deq_get_id(char const* name);

/* Description: get table size,
**              extinguished by table type(map, map_sec, vector and deque).
** Parameter: table id
** Return: table size
*/
size_t rtm_map_get_size(int id);
size_t rtm_map_seq_get_size(int id);
size_t rtm_vec_get_size(int id);
size_t rtm_deq_get_size(int id);

/* Description: check if specifed key exists, in specifed map table
** Parameter: id: map table id, returned by `rtm_map_get_id`
**            key: key name.
** Return: false: not exists;
**         true: exists.
*/
int rtm_map_has_key(int id, char const* key);
int rtm_map_seq_has_key(int id, char const* key);

/* Description: get all keys of specifed map table
** Parameter: map table id
** Return: pointer to RtmRet. error accured if equals null.
**         `num`: num of keys
**         `s_val`: array of key strings
**         `i_val`: array of value types,
**                  only signalin, signalout, para, chanin, chanout have valid value type,
**                  equals null otherwise.
*/
RtmRet* rtm_map_get_all_keys(int id);

/* Description: get all keys of specifed map_seq table
** Parameter: map_seq table id
** Return: pointer to RtmRet. error accured if equals null.
**         `num`: num of keys
**         `s_val`: array of key strings
**         `i_val`: array of value type,
**                  only signalin, signalout, para, chanin, chanout have valid value type,
**                  equals null otherwise.
**         `j_val`: sequence size of values
*/
RtmRet* rtm_map_seq_get_all_keys(int id);

/* Description: get value, specifed by key, in specifed map table
** Parameter: id: map table id;
**            key: key name.
** Return: void*: pointer to value,
**                type of which is specifed by results return by `rtm_map_get_all_keys`,
**                error accured if equals null.
*/
void* rtm_map_get_value(int id, char const* key);
void* rtm_map_seq_get_value(int id, char const* key, int i);

/* Description: set value, specifed by key, in specifed map table
** Parameter: id: map table id;
**            key: key name;
**            value: pointer to value,
**            value_type: returned by `rtm_map_get_all_keys`,
**            value_size: if value_type is string, then it should be strlen(value),
**                        otherwise should be sizeof(T).
** Return: 0: success;
**         -1: failed.
*/
int rtm_map_set_value(int id, char const* key, void const* value, int value_type, size_t value_size);

/* Description: set value, specifed by key, in specifed map_seq table
** Parameter: id: map_seq table id;
**            key: key name;
**            i: value index of sequence
**            value: pointer to value,
**            value_type: returned by `rtm_map_get_all_keys`,
**            value_size: if value_type is string, then it should be strlen(value),
**                        otherwise should be sizeof(T).
** Return: 0: success;
**         -1: failed.
*/
int rtm_map_seq_set_value(int id, char const* key, int i, void const* value, int value_type, size_t value_size);

/* Description: get all values of specifed vec table
** Parameter: vec table id
** Return: pointer to RtmRet. error accured if equals null.
**         `num`: num of values
**         `s_val`: array of value strings
**         `i_val`: null
*/
RtmRet* rtm_vec_get_all_values(int id);

/* Description: get all values of specifed vec table
** Parameter: vec table name
** Return: pointer to RtmRet. error accured if equals null.
**         `num`: num of values
**         `s_val`: array of value strings
**         `i_val`: null
*/
RtmRet* rtm_vec_get_all_values_by_name(char const* name);

/* Description: get specifed num values from specifed index
** Parameter: vec table id
** Return: pointer to RtmRet. error accured if equals null.
**         `num`: should equals len
**         `s_val`: array of value strings
**         `i_val`: null
*/
RtmRet* rtm_vec_get_values(int id, int st, int len);

/* Description: get all value in specifed deq table
** Parameter: id: deq table id;
** Return: pointer to RtmRet. error accured if equals null.
**         `num`: num of values
**         `s_val`: array of value strings
**         `i_val`: null
*/
RtmRet* rtm_deq_get_all_value(int id);

/* Description: get all value in specifed deq table
** Parameter: name: deq table name;
** Return: pointer to RtmRet. error accured if equals null.
**         `num`: num of values
**         `s_val`: array of value strings
**         `i_val`: null
*/
RtmRet* rtm_deq_get_all_value_by_name(char const* name);

/* Description: pop all value in specifed deq table
** Parameter: id: deq table id;
** Return: pointer to RtmRet. error accured if equals null.
**         `num`: num of values
**         `s_val`: array of value strings
**         `i_val`: null
*/
RtmRet* rtm_deq_pop_all_value(int id);

/* Description: pop all value in specifed deq table
** Parameter: name: deq table name;
** Return: pointer to RtmRet. error accured if equals null.
**         `num`: num of values
**         `s_val`: array of value strings
**         `i_val`: null
*/
RtmRet* rtm_deq_pop_all_value_by_name(char const* name);

/* Description: push back one value into specifed deq table
** Parameter: id: deq table id;
**            str: pointer to value, string type
**            len: should be strlen(str)
** Return: 0: success;
**         -1: failed.
*/
int rtm_deq_push_back_value(int id, void const* str, size_t len);

/* Description: push back one value into specifed deq table
** Parameter: name: deq table name;
**            str: pointer to value, string type
**            len: should be strlen(str)
** Return: 0: success;
**         -1: failed.
*/
int rtm_deq_push_back_value_by_name(char const* name, void const* str, size_t len);

/* Description: struct of returned addr info
** `name`: un-indexed addr prefix
** `index`: addr index
** `type_signal`: signal type id
** `type_value`: signal value type id
** `type_signal_desc`: signal type description
** `type_value_desc`: signal value type description
** `value_min`: min value for para signal
** `value_max`: max value for para signal
** `immutable`: value immutable
*/
typedef struct {
    char* name;
    int index;

    int type_signal;
    int type_value;

    char* type_signal_desc;
    char* type_value_desc;

    char* value_min;
    char* value_max;

    int immutable;
} AddrRet;

/* Description: free returned dynamic value. any RtmRes return by funcs should be freed.
** Parameter: pointer to RtmRes
** Return: void
*/
void rtm_free_addr(AddrRet*);

/* Description: try to find specified signal(signalout, signalin, para)
** Parameter: wanted address
** Return: pointer to AddrRet. addr isnot present if equals null.
*/
AddrRet* rtm_find_signal(char const* addr);

/* Description: try to find specified chan signal(chanout, chanin)
** Parameter: wanted address
** Return: pointer to AddrRet. addr isnot present if equals null.
*/
AddrRet* rtm_find_chan(char const* addr);

/* Description: try to update para
** Parameter: addr: pointer to AddrRet, which is wanted para address
**            value: pointer to wanted written val
**            value_size: if value_type is string, then it should be strlen(value),
**                        otherwise should be sizeof(T).
** Return: 0: success;
**         -1: failed.
** Note: composited operations, same as `RemoteRipe::write_para`
*/
int update_para(AddrRet* addr, void const* value, size_t value_size);

#ifdef __cplusplus
}
#endif
