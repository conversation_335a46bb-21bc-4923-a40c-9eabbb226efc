use std::{
    ffi::{c_int, c_void, CString},
    io::{<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>,Result},
    ptr::null_mut,
};

use crate::bindings::*;
use std::ops::Range;

use libloading::os::unix::Symbol;
pub struct RTMMemory {
    rtm_init: Symbol<RTMInitT>,
    rtm_setup: Option<Symbol<RtmSetupT>>,
    rtm_free_ret: Symbol<RtmFreeRetT>,
    rtm_map_get_all_id: Symbol<RtmMapGetAllIdT>,
    rtm_map_seq_get_all_id: Symbol<RtmMapSeqGetAllIdT>,
    rtm_deq_get_all_id: Symbol<RtmDeqGetAllIdT>,
    rtm_vec_get_all_id: Symbol<RtmVecGetAllIdT>,
    rtm_map_get_size: Symbol<RtmMapGetSizeT>,
    rtm_map_seq_get_size: Symbol<RtmMapSeqGetSizeT>,
    rtm_deq_get_size: Symbol<RtmDeqGetSizeT>,
    rtm_vec_get_size: Symbol<RtmVecGetSizeT>,
    rtm_map_has_key: Symbol<RtmMapHasKeyT>,
    rtm_map_seq_has_key: Symbol<RtmMapSeqHasKeyT>,
    rtm_map_get_all_keys: Symbol<RtmMapGetAllKeysT>,
    rtm_map_seq_get_all_keys: Symbol<RtmMapSeqGetAllKeysT>,
    rtm_map_get_value: Symbol<RtmMapGetValueT>,
    #[allow(unused)]
    rtm_map_set_value: Symbol<RtmMapSetValueT>,
    rtm_map_seq_get_value: Symbol<RtmMapSeqGetValueT>,
    #[allow(unused)]
    rtm_map_seq_set_value: Symbol<RtmMapSeqSetValueT>,
    rtm_vec_get_all_values: Symbol<RtmVecGetAllValuesT>,
    rtm_vec_get_values: Symbol<RtmVecGetValuesT>,
    rtm_deq_get_all_value: Symbol<RtmDeqGetAllValueT>,
    rtm_deq_pop_all_value: Symbol<RtmDeqPopAllValueT>,
    rtm_deq_push_back_value: Symbol<RtmDeqPushBackValueT>,
    #[allow(unused)]
    lib: libloading::Library,
}

impl RTMMemory {
    pub fn new() -> core::result::Result<RTMMemory, libloading::Error> {
        let lib = unsafe { libloading::Library::new("/deri/mgc/libdrp.so")? };
        unsafe {
            Ok(RTMMemory {
                rtm_init: lib.get::<RTMInitT>(b"rtm_init")?.into_raw(),
                rtm_setup: lib
                    .get::<RtmSetupT>(b"rtm_setup")
                    .ok()
                    .map(|e| e.into_raw()),
                rtm_free_ret: lib.get::<RtmFreeRetT>(b"rtm_free_ret")?.into_raw(),
                rtm_map_get_all_id: lib
                    .get::<RtmMapGetAllIdT>(b"rtm_map_get_all_id")?
                    .into_raw(),
                rtm_map_seq_get_all_id: lib
                    .get::<RtmMapSeqGetAllIdT>(b"rtm_map_seq_get_all_id")?
                    .into_raw(),
                rtm_deq_get_all_id: lib
                    .get::<RtmDeqGetAllIdT>(b"rtm_deq_get_all_id")?
                    .into_raw(),
                rtm_vec_get_all_id: lib
                    .get::<RtmVecGetAllIdT>(b"rtm_vec_get_all_id")?
                    .into_raw(),
                rtm_map_get_size: lib.get::<RtmMapGetSizeT>(b"rtm_map_get_size")?.into_raw(),
                rtm_map_seq_get_size: lib
                    .get::<RtmMapSeqGetSizeT>(b"rtm_map_seq_get_size")?
                    .into_raw(),
                rtm_deq_get_size: lib.get::<RtmDeqGetSizeT>(b"rtm_deq_get_size")?.into_raw(),
                rtm_vec_get_size: lib.get::<RtmVecGetSizeT>(b"rtm_vec_get_size")?.into_raw(),
                rtm_map_has_key: lib.get::<RtmMapHasKeyT>(b"rtm_map_has_key")?.into_raw(),
                rtm_map_seq_has_key: lib
                    .get::<RtmMapSeqHasKeyT>(b"rtm_map_seq_has_key")?
                    .into_raw(),
                rtm_map_get_all_keys: lib
                    .get::<RtmMapGetAllKeysT>(b"rtm_map_get_all_keys")?
                    .into_raw(),
                rtm_map_seq_get_all_keys: lib
                    .get::<RtmMapSeqGetAllKeysT>(b"rtm_map_seq_get_all_keys")?
                    .into_raw(),
                rtm_map_get_value: lib.get::<RtmMapGetValueT>(b"rtm_map_get_value")?.into_raw(),
                rtm_map_set_value: lib.get::<RtmMapSetValueT>(b"rtm_map_set_value")?.into_raw(),
                rtm_map_seq_get_value: lib
                    .get::<RtmMapSeqGetValueT>(b"rtm_map_seq_get_value")?
                    .into_raw(),
                rtm_map_seq_set_value: lib
                    .get::<RtmMapSeqSetValueT>(b"rtm_map_seq_set_value")?
                    .into_raw(),
                rtm_vec_get_all_values: lib
                    .get::<RtmVecGetAllValuesT>(b"rtm_vec_get_all_values")?
                    .into_raw(),
                rtm_vec_get_values: lib
                    .get::<RtmVecGetValuesT>(b"rtm_vec_get_values")?
                    .into_raw(),
                rtm_deq_get_all_value: lib
                    .get::<RtmDeqGetAllValueT>(b"rtm_deq_get_all_value")?
                    .into_raw(),
                rtm_deq_pop_all_value: lib
                    .get::<RtmDeqPopAllValueT>(b"rtm_deq_pop_all_value")?
                    .into_raw(),
                rtm_deq_push_back_value: lib
                    .get::<RtmDeqPushBackValueT>(b"rtm_deq_push_back_value")?
                    .into_raw(),
                lib: lib,
            })
        }
    }

    pub fn reinit(&self) -> Result<()> {
        unsafe {
            let ret = if self.rtm_setup.is_some() {
                (self.rtm_setup.as_ref().unwrap())(1)
            } else {
                (self.rtm_init)()
            };
            if ret < 0 {
                return Err(Error::other("rtm init failed"));
            }
        }
        Ok(())
    }

    pub(crate) fn get_tables(&self, f: impl Fn() -> *mut RtmRet) -> Vec<Table> {
        let mut tables = vec![];
        unsafe {
            let ret_ptr = f();
            read_rtm_ret(ret_ptr)
                .unwrap_or_else(|| RTMReturn::default())
                .elements
                .into_iter()
                .for_each(|el| {
                    tables.push(Table {
                        id: el.i,
                        name: el.s,
                    })
                });
            (self.rtm_free_ret)(ret_ptr);
        }
        tables
    }

    pub fn get_all_maps(&self) -> Vec<Table> {
        self.get_tables(|| unsafe { (self.rtm_map_get_all_id)() })
    }

    pub fn get_all_sequences(&self) -> Vec<Table> {
        self.get_tables(|| unsafe { (self.rtm_map_seq_get_all_id)() })
    }

    pub fn get_all_dequeues(&self) -> Vec<Table> {
        self.get_tables(|| unsafe { (self.rtm_deq_get_all_id)() })
    }

    pub fn get_all_vectors(&self) -> Vec<Table> {
        self.get_tables(|| unsafe { (self.rtm_vec_get_all_id)() })
    }

    pub fn get_map_size(&self, id: i32) -> usize {
        unsafe { (self.rtm_map_get_size)(id) }
    }

    pub fn get_sequence_size(&self, id: i32) -> usize {
        unsafe { (self.rtm_map_seq_get_size)(id) }
    }

    pub fn get_deq_size(&self, id: i32) -> usize {
        unsafe { (self.rtm_deq_get_size)(id) }
    }

    pub fn get_vec_size(&self, id: i32) -> usize {
        unsafe { (self.rtm_vec_get_size)(id) }
    }

    pub fn map_has_key(&self, id: i32, key: &str) -> i32 {
        has_key(
            |id, key| unsafe { (self.rtm_map_has_key)(id, key) },
            id,
            key,
        )
    }

    pub fn sequence_has_key(&self, id: i32, key: &str) -> i32 {
        has_key(
            |id, key| unsafe { (self.rtm_map_seq_has_key)(id, key) },
            id,
            key,
        )
    }

    pub fn get_table_keys(&self, id: i32) -> Vec<(String, i32)> {
        let mut ret = vec![];
        unsafe {
            let c_ret = (self.rtm_map_get_all_keys)(id);
            read_rtm_ret(c_ret)
                .unwrap_or_else(|| RTMReturn::default())
                .elements
                .into_iter()
                .for_each(|el| {
                    ret.push((el.s, el.i));
                });
            (self.rtm_free_ret)(c_ret);
        }
        ret
    }

    pub fn get_sequence_keys(&self, id: i32) -> Vec<(String, i32, i32)> {
        let mut ret = vec![];
        unsafe {
            let c_ret = (self.rtm_map_seq_get_all_keys)(id);
            read_rtm_ret(c_ret)
                .unwrap_or_else(|| RTMReturn::default())
                .elements
                .into_iter()
                .for_each(|el| {
                    ret.push((el.s, el.i, el.j));
                });
            (self.rtm_free_ret)(c_ret);
        }
        ret
    }

    pub fn get_map_value(&self, id: i32, key: &str) -> Result<RTMValue> {
        let id = id.into();
        let dest = self.get_table_keys(id).into_iter().find(|el| el.0.eq(key));
        if dest.is_none() {
            return Err(Error::new(ErrorKind::Other, "key not found"));
        }
        let c_string = CString::new(key).expect("CString conversion failed");
        let c_string_ptr = c_string.as_ptr();
        unsafe {
            Ok(RTMValue::new(
                dest.unwrap().1 as u32,
                (self.rtm_map_get_value)(id, c_string_ptr),
            ))
        }
    }

    pub fn get_map_value_ptr(&self, id: i32, key: &str) -> *const c_void {
        let id = id.into();
        let c_string = CString::new(key).expect("CString conversion failed");
        let c_string_ptr = c_string.as_ptr();
        unsafe { (self.rtm_map_get_value)(id, c_string_ptr) }
    }

    pub fn get_sequence_value(&self, id: i32, key: &str, mut idx: i32) -> Result<RTMValue> {
        let id = id.into();
        idx -= 1;
        let dest = self
            .get_sequence_keys(id)
            .into_iter()
            .find(|el| el.0.eq(key));
        if dest.is_none() {
            return Err(Error::new(ErrorKind::Other, "key not found"));
        }
        let c_string = CString::new(key).expect("CString conversion failed");
        let c_string_ptr = c_string.as_ptr();
        unsafe {
            let ptr: *mut std::ffi::c_void = (self.rtm_map_seq_get_value)(id, c_string_ptr, idx);
            assert!(ptr != null_mut());
            Ok(RTMValue::new(dest.unwrap().1 as u32, ptr))
        }
    }

    pub fn get_sequence_value_ptr(&self, id: i32, key: &str, mut idx: i32) -> *const c_void {
        idx -= 1;
        let c_string = CString::new(key).expect("CString conversion failed");
        let c_string_ptr = c_string.as_ptr();
        unsafe {
            let ptr: *mut std::ffi::c_void = (self.rtm_map_seq_get_value)(id, c_string_ptr, idx);
            ptr
        }
    }

    pub fn get_vector_values(&self, id: i32) -> Vec<String> {
        unsafe {
            let ret = (self.rtm_vec_get_all_values)(id);
            let r = read_rtm_ret(ret);
            (self.rtm_free_ret)(ret);
            get_strings(r)
        }
    }

    pub fn get_vector_values_range(&self, id: i32, r: Range<i32>) -> Vec<String> {
        unsafe {
            let ret = (self.rtm_vec_get_values)(id, r.start, r.len() as c_int);
            let r = read_rtm_ret(ret);
            (self.rtm_free_ret)(ret);
            get_strings(r)
        }
    }

    pub fn get_dequeue_values(&self, id: i32) -> Vec<String> {
        unsafe {
            let ret = (self.rtm_deq_get_all_value)(id);
            let r = read_rtm_ret(ret);
            (self.rtm_free_ret)(ret);
            get_strings(r)
        }
    }

    pub fn pop_dequeue_values(&self, id: i32) -> Vec<String> {
        unsafe {
            let ret = (self.rtm_deq_pop_all_value)(id);
            let r = read_rtm_ret(ret);
            (self.rtm_free_ret)(ret);
            get_strings(r)
        }
    }
    
    pub fn push_dequeue_values(&self, id: i32, val: String) -> Result<()> {
        unsafe {
            let len = val.len();
            let c_string = CString::new(val).expect("CString conversion failed");
            let c_string_ptr = c_string.as_ptr();
            if 0 == (self.rtm_deq_push_back_value)(id, c_string_ptr as *mut c_void, len) {
                Ok(())
            } else {
                Err(Error::new(ErrorKind::Other, "set failed"))
            }
        }
    }
}

#[cfg(test)]
mod tests {
    use crate::rtm::RTMMemory;
    #[test]
    fn test_rtm() {
        let rtm = RTMMemory::new().unwrap();
        rtm.reinit().unwrap();
        assert_ne!(rtm.get_all_dequeues().len(),0);
        assert_ne!(rtm.get_all_maps().len(),0);
        assert_ne!(rtm.get_all_sequences().len(),0);
        assert_ne!(rtm.get_all_vectors().len(),0);
    }

}