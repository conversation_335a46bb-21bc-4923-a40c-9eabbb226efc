use crate::bindings::Value;
use crate::rtm::RTMMemory;
use crate::RTMValue;
use lazy_static::lazy_static;
use log::debug;
use num_enum::IntoPrimitive;
use regex::Regex;
use std::collections::HashMap;
use std::io::{<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Result};
lazy_static! {
    static ref SHORT_ADDR_REGEXP: Regex =
        Regex::new(r"C1_(\w+(_\w+)*)\.(\w+(_\w+)*)(\[(\d+)\])?\.(\w+(_\w+)*)(\[(\d+)\])?").unwrap();
}
#[derive(Debug, IntoPrimitive)]
#[repr(i32)]
pub(crate) enum TableIndex {
    SignalIn = 0,
    SignalOut = 2,
    Para = 4,
    Linkage = 8,
}

#[derive(Debug, PartialEq, Eq)]
pub struct Addr {
    pub proc: std::ops::Range<usize>,
    pub component: (std::ops::Range<usize>, Option<std::ops::Range<usize>>),
    pub cell: (std::ops::Range<usize>, Option<std::ops::Range<usize>>),
    original: String,
}

pub struct ComponentAddr<'a> {
    pub proc: &'a str,
    pub class: &'a str,
    pub index: Option<&'a str>,
    pub addr: &'a str,
}

impl<'a> ComponentAddr<'a> {
    pub(crate) fn name(&self) -> String {
        match self.index {
            Some(idx) => {
                format!("{}[{}]", self.class, idx)
            }
            None => self.class.to_string(),
        }
    }
}

impl Addr {
    pub fn component_addr(&self) -> ComponentAddr {
        ComponentAddr {
            proc: &self.original[self.proc.clone()],
            class: &self.original[self.component.0.clone()],
            index: self.component.1.as_ref().map(|e| &self.original[e.clone()]),
            addr: self
                .component
                .1
                .as_ref()
                .map_or(&self.original[0..self.component.0.end], |e| {
                    &self.original[0..e.end + 1]
                }),
        }
    }

    pub fn split(&self) -> (&str, Option<usize>) {
        (
            &self.original.as_str()[0..self.cell.0.end],
            self.cell_index(),
        )
    }

    pub fn cell_name(&self) -> &str {
        &self.original.as_str()[self.cell.0.clone()]
    }

    pub fn cell_index(&self) -> Option<usize> {
        self.cell
            .1
            .as_ref()
            .map(|e| self.original[e.clone()].parse::<usize>().unwrap())
    }
}

impl TryFrom<&str> for Addr {
    type Error = Error;
    //                 1   2         3   4       5  6          7   8        9  10
    // Regex::new(r"C1_(\w+(_\w+)*)\.(\w+(_\w+)*)(\[(\d+)\])?\.(\w+(_\w+)*)(\[(\d+)\])?")
    fn try_from(ret: &str) -> Result<Self> {
        if let Some(caps) = SHORT_ADDR_REGEXP.captures(ret) {
            Ok(Addr {
                original: ret.to_string(),
                proc: caps.get(1).unwrap().range(),
                component: (
                    caps.get(3).unwrap().range(),
                    caps.get(6).map_or(None, |e| Some(e.range())),
                ),
                cell: (
                    caps.get(7).unwrap().range(),
                    caps.get(10).map_or(None, |e| Some(e.range())),
                ),
            })
        } else {
            Err(Error::new(ErrorKind::Other, "invalid addr"))
        }
    }
}

#[cfg(test)]
mod tests {
    use super::Addr;
    #[test]

    fn test_addr_parse_kind1() {
        let addr = Addr::try_from("C1_sample.instance[19].volt[34]").unwrap();
        assert_eq!(addr.cell_index(), Some(34));
        assert_eq!(addr.cell_name(), "volt");
        assert_eq!(addr.split(), ("C1_sample.instance[19].volt", Some(34)));

        let cp_addr = addr.component_addr();
        assert_eq!(cp_addr.name(), "instance[19]");
        assert_eq!(cp_addr.class, "instance");
        assert_eq!(cp_addr.index, Some("19"));
        assert_eq!(cp_addr.addr, "C1_sample.instance[19]");
        assert_eq!(cp_addr.proc, "sample");
    }

    #[test]
    fn test_addr_parse_kind2() {
        let addr = Addr::try_from("C1_ctl.component[3].signal").unwrap();
        assert_eq!(addr.cell_name(), "signal");
        assert_eq!(addr.cell_index(), None);
        assert_eq!(addr.split(), ("C1_ctl.component[3].signal", None));

        let cp_addr = addr.component_addr();
        assert_eq!(cp_addr.name(), "component[3]");
        assert_eq!(cp_addr.class, "component");
        assert_eq!(cp_addr.index, Some("3"));
        assert_eq!(cp_addr.addr, "C1_ctl.component[3]");
        assert_eq!(cp_addr.proc, "ctl");
    }

    #[test]
    fn test_addr_parse_kind3() {
        let addr = Addr::try_from("C1_ctl.component.signal").unwrap();
        assert_eq!(addr.cell_name(), "signal");
        assert_eq!(addr.cell_index(), None);
        assert_eq!(addr.split(), ("C1_ctl.component.signal", None));

        let cp_addr = addr.component_addr();
        assert_eq!(cp_addr.name(), "component");
        assert_eq!(cp_addr.class, "component");
        assert_eq!(cp_addr.index, None);
        assert_eq!(cp_addr.addr, "C1_ctl.component");
        assert_eq!(cp_addr.proc, "ctl");
    }
}

#[derive(Debug)]
pub enum Signal {
    SignalOut(Addr, RTMValue),
    SignalIn(Addr, Option<(Addr, RTMValue)>),
    Para(Addr, RTMValue),
}

impl Signal {
    pub fn read(&self) -> Result<Value> {
        match self {
            Signal::SignalOut(_, v) => Ok(v.read()?),
            Signal::SignalIn(_, linked) => Ok(linked
                .as_ref()
                .ok_or(Error::other("unlinked signal in"))?
                .1
                .read()?),
            Signal::Para(_, v) => Ok(v.read()?),
        }
    }

    pub fn address(&self) -> &Addr {
        match self {
            Signal::SignalOut(addr, _) => addr,
            Signal::SignalIn(addr, _) => addr,
            Signal::Para(addr, _) => addr,
        }
    }

    pub fn is_linked(&self) -> bool {
        if let Signal::SignalIn(_, linked) = self {
            linked.is_some()
        } else {
            false
        }
    }
}

pub struct Cache {
    rtm: crate::rtm::RTMMemory,
    pub components: Vec<Component>,
}

pub struct Component {
    pub name: String,
    pub index: Option<i32>,
    #[allow(unused)]
    pub proc: String,
    pub signals: HashMap<String, Signal>,
}

impl Component {
    pub fn new(addr: ComponentAddr) -> Self {
        Component {
            name: addr.class.to_string(),
            index: addr.index.map(|e| e.parse::<i32>().ok()).flatten(),
            proc: addr.proc.to_string(),
            signals: HashMap::new(),
        }
    }
    fn add_signal(&mut self, signal: Signal) {
        match &signal {
            Signal::SignalOut(addr, _) => {
                self.signals.insert(addr.split().0.to_string(), signal);
            }
            Signal::SignalIn(addr, _) => {
                self.signals.insert(addr.split().0.to_string(), signal);
            }
            Signal::Para(addr, _) => {
                self.signals.insert(addr.split().0.to_string(), signal);
            }
        }
    }
}

impl Cache {
    pub fn new() -> Result<Cache> {
        let r = RTMMemory::new().map_err(|e| Error::other(e.to_string()))?;
        Ok(Cache {
            components: Default::default(),
            rtm: r,
        })
    }

    pub fn new_with_filter<T>(filter: T) -> Result<Self>
    where
        T: Fn(&Addr) -> bool,
    {
        let rtm = RTMMemory::new().map_err(|e| Error::other(e.to_string()))?;
        let mut c = Cache {
            components: Default::default(),
            rtm: rtm,
        };
        c.reinit(filter)?;
        return Ok(c);
    }

    fn filter_addr<'a, I, T>(iter: I, f: T) -> impl Iterator<Item = (Addr, u32)>
    where
        I: Iterator<Item = (String, i32)> + 'a,
        T: Fn(&Addr) -> bool + 'a,
    {
        iter.filter_map(move |e| match Addr::try_from(e.0.as_str()) {
            Ok(a) => {
                if f(&a) {
                    Some((a, e.1 as u32))
                } else {
                    None
                }
            }
            Err(_) => None,
        })
    }

    pub fn reinit<T>(&mut self, filter: T) -> Result<()>
    where
        T: Fn(&Addr) -> bool,
    {
        debug!("reinit rtm");
        self.components.clear();
        self.rtm.reinit()?;
        debug!("reinit rtm ok");
        let mut components: HashMap<String, Component> = HashMap::new();
        for (addr, tp) in Self::filter_addr(
            self.rtm
                .get_table_keys(TableIndex::SignalOut.into())
                .into_iter(),
            &filter,
        ) {
            let cp_addr = addr.component_addr();
            components
                .entry(cp_addr.name())
                .or_insert_with(|| Component::new(cp_addr))
                .add_signal(self.new_signal_out(addr, tp as u32));
        }

        for (addr, tp) in Self::filter_addr(
            self.rtm
                .get_table_keys(TableIndex::SignalIn.into())
                .into_iter(),
            &filter,
        ) {
            let cp_addr = addr.component_addr();
            let component = components
                .entry(cp_addr.name())
                .or_insert_with(|| Component::new(cp_addr));
            match self.new_signal_in(addr, tp) {
                Ok(signal) => {
                    if signal.is_linked() {
                        component.add_signal(signal);
                    }
                }
                Err(_) => {}
            }
        }

        for (addr, tp) in Self::filter_addr(
            self.rtm.get_table_keys(TableIndex::Para.into()).into_iter(),
            &filter,
        ) {
            let cp_addr = addr.component_addr();
            components
                .entry(cp_addr.name())
                .or_insert_with(|| Component::new(cp_addr))
                .add_signal(self.new_para(addr, tp));
        }
        self.components = components.into_iter().map(|e| e.1).collect();
        debug!("rtm init ok components num = {}", self.components.len());
        Ok(())
    }

    pub fn new_signal_out(&self, addr: Addr, tp: u32) -> Signal {
        let cell = addr.split();
        match cell.1 {
            None => {
                let val = RTMValue::new(
                    tp,
                    self.rtm
                        .get_map_value_ptr(TableIndex::SignalOut.into(), cell.0),
                );
                Signal::SignalOut(addr, val)
            }
            Some(idx) => {
                let val = RTMValue::new(
                    tp,
                    self.rtm.get_sequence_value_ptr(
                        TableIndex::SignalOut.into(),
                        cell.0,
                        idx as i32,
                    ),
                );
                Signal::SignalOut(addr, val)
            }
        }
    }

    pub fn new_para(&self, addr: Addr, tp: u32) -> Signal {
        let cell = addr.split();
        match addr.cell_index() {
            None => {
                let val = RTMValue::new(
                    tp,
                    self.rtm.get_map_value_ptr(TableIndex::Para.into(), cell.0),
                );
                Signal::Para(addr, val)
            }
            Some(idx) => {
                let val = RTMValue::new(
                    tp,
                    self.rtm
                        .get_sequence_value_ptr(TableIndex::Para.into(), cell.0, idx as i32),
                );
                Signal::Para(addr, val)
            }
        }
    }

    pub fn new_signal_in(&self, addr: Addr, tp: u32) -> Result<Signal> {
        use crate::bindings::Value;
        let map_value_ptr = self
            .rtm
            .get_map_value_ptr(TableIndex::Linkage.into(), &addr.original);
        if !map_value_ptr.is_null() {
            if let Value::String(sig_out_addr) =
                RTMValue::new(crate::bindings::RTMC_SIGNAL_TYPE_STRING, map_value_ptr).read()?
            {
                let linked_with = Addr::try_from(sig_out_addr.as_str())?;
                if let Signal::Para(a, v) = self.new_para(linked_with, tp) {
                    if !v.is_empty() {
                        return Ok(Signal::SignalIn(addr, Some((a, v))));
                    }
                    if let Signal::SignalOut(a, v) = self.new_signal_out(a, tp) {
                        if !v.is_empty() {
                            return Ok(Signal::SignalIn(addr, Some((a, v))));
                        }
                    }
                }
                log::warn!(
                    "{} link with {}(neither para or signalout)",
                    &addr.original,
                    &sig_out_addr
                );
            }
        }
        Ok(Signal::SignalIn(addr, None))
    }
}
