use std::ffi::{c_char, CStr, CString};
use std::io::{<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>};
use std::mem::size_of_val;
use std::os::raw::{c_int, c_void};
use std::ptr::{self};
use std::slice;

pub type wchar_t = ::std::os::raw::c_uint;

#[repr(C)]
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>)]
pub struct max_align_t {
    pub __clang_max_align_nonce1: ::std::os::raw::c_longlong,
    pub __clang_max_align_nonce2: f64,
}

#[test]
fn bindgen_test_layout_max_align_t() {
    const UNINIT: ::std::mem::MaybeUninit<max_align_t> = ::std::mem::MaybeUninit::uninit();
    let ptr = UNINIT.as_ptr();
    assert_eq!(
        ::std::mem::size_of::<max_align_t>(),
        16usize,
        concat!("Size of: ", stringify!(max_align_t))
    );
    assert_eq!(
        ::std::mem::align_of::<max_align_t>(),
        8usize,
        concat!("Alignment of ", stringify!(max_align_t))
    );
    assert_eq!(
        unsafe { ::std::ptr::addr_of!((*ptr).__clang_max_align_nonce1) as usize - ptr as usize },
        0usize,
        concat!(
        "Offset of field: ",
        stringify!(max_align_t),
        "::",
        stringify!(__clang_max_align_nonce1)
        )
    );
    assert_eq!(
        unsafe { ::std::ptr::addr_of!((*ptr).__clang_max_align_nonce2) as usize - ptr as usize },
        8usize,
        concat!(
        "Offset of field: ",
        stringify!(max_align_t),
        "::",
        stringify!(__clang_max_align_nonce2)
        )
    );
}

pub const RTMC_SIGNALIN: _bindgen_ty_1 = 1;
pub const RTMC_SIGNALOUT: _bindgen_ty_1 = 2;
pub const RTMC_PARA: _bindgen_ty_1 = 3;
pub const RTMC_CHANIN: _bindgen_ty_1 = 4;
pub const RTMC_CHANOUT: _bindgen_ty_1 = 5;

pub type _bindgen_ty_1 = ::std::os::raw::c_uint;

pub const RTMC_SIGNAL_TYPE_UINT8: _bindgen_ty_2 = 1;
pub const RTMC_SIGNAL_TYPE_UINT16: _bindgen_ty_2 = 2;
pub const RTMC_SIGNAL_TYPE_UINT32: _bindgen_ty_2 = 3;
pub const RTMC_SIGNAL_TYPE_UINT64: _bindgen_ty_2 = 4;
pub const RTMC_SIGNAL_TYPE_INT8: _bindgen_ty_2 = 5;
pub const RTMC_SIGNAL_TYPE_INT16: _bindgen_ty_2 = 6;
pub const RTMC_SIGNAL_TYPE_INT32: _bindgen_ty_2 = 7;
pub const RTMC_SIGNAL_TYPE_INT64: _bindgen_ty_2 = 8;
pub const RTMC_SIGNAL_TYPE_FLOAT: _bindgen_ty_2 = 9;
pub const RTMC_SIGNAL_TYPE_DOUBLE: _bindgen_ty_2 = 10;
pub const RTMC_SIGNAL_TYPE_STRING: _bindgen_ty_2 = 11;
pub const RTMC_SIGNAL_TYPE_NUM: _bindgen_ty_2 = 12;

pub type _bindgen_ty_2 = ::std::os::raw::c_uint;

pub const RTMC_MAP_SIGNALIN_VAL: _bindgen_ty_3 = 0;
pub const RTMC_MAP_SIGNALIN_JSON: _bindgen_ty_3 = 1;
pub const RTMC_MAP_SIGNALOUT_VAL: _bindgen_ty_3 = 2;
pub const RTMC_MAP_SIGNALOUT_JSON: _bindgen_ty_3 = 3;
pub const RTMC_MAP_PARA_VAL: _bindgen_ty_3 = 4;
pub const RTMC_MAP_PARA_JSON: _bindgen_ty_3 = 5;
pub const RTMC_MAP_QUEUEIN_JSON: _bindgen_ty_3 = 6;
pub const RTMC_MAP_QUEUEOUT_JSON: _bindgen_ty_3 = 7;
pub const RTMC_MAP_SIGNAL_MAP: _bindgen_ty_3 = 8;
pub const RTMC_MAP_PROCESS_STAGE: _bindgen_ty_3 = 9;
pub const RTMC_MAP_MAX_NUM: _bindgen_ty_3 = 10;

pub type _bindgen_ty_3 = ::std::os::raw::c_uint;

pub const RTMC_MAP_SIGNALIN_SEQ_VAL: _bindgen_ty_4 = 0;
pub const RTMC_MAP_SIGNALIN_SEQ_JSON: _bindgen_ty_4 = 1;
pub const RTMC_MAP_SIGNALOUT_SEQ_VAL: _bindgen_ty_4 = 2;
pub const RTMC_MAP_SIGNALOUT_SEQ_JSON: _bindgen_ty_4 = 3;
pub const RTMC_MAP_PARA_SEQ_VAL: _bindgen_ty_4 = 4;
pub const RTMC_MAP_PARA_SEQ_JSON: _bindgen_ty_4 = 5;
pub const RTMC_MAP_QUEUEIN_SEQ_JSON: _bindgen_ty_4 = 6;
pub const RTMC_MAP_QUEUEOUT_SEQ_JSON: _bindgen_ty_4 = 7;
pub const RTMC_MAP_SEQ_MAX_NUM: _bindgen_ty_4 = 8;

pub type _bindgen_ty_4 = ::std::os::raw::c_uint;

pub const RTMC_VEC_ANA_REF_TABLE: _bindgen_ty_5 = 0;
pub const RTMC_VEC_BI_REF_TABLE: _bindgen_ty_5 = 1;
pub const RTMC_VEC_SOE_REF_TABLE: _bindgen_ty_5 = 2;
pub const RTMC_VEC_ALARM_REF_TABLE: _bindgen_ty_5 = 3;
pub const RTMC_VEC_TRIP_REF_TABLE: _bindgen_ty_5 = 4;
pub const RTMC_VEC_ORDER_REF_TABLE: _bindgen_ty_5 = 5;
pub const RTMC_VEC_SYS_PARA_REF_TABLE: _bindgen_ty_5 = 6;
pub const RTMC_VEC_DEV_PARA_REF_TABLE: _bindgen_ty_5 = 7;
pub const RTMC_VEC_CTL_PARA_REF_TABLE: _bindgen_ty_5 = 8;
pub const RTMC_VEC_PROT_PARA_REF_TABLE: _bindgen_ty_5 = 9;
pub const RTMC_VEC_COMM_PARA_REF_TABLE: _bindgen_ty_5 = 10;
pub const RTMC_VEC_WAVE_TRIG_REF_TABLE: _bindgen_ty_5 = 11;
pub const RTMC_VEC_WAVE_AI_CHAN_REF_TABLE: _bindgen_ty_5 = 12;
pub const RTMC_VEC_WAVE_DI_CHAN_REF_TABLE: _bindgen_ty_5 = 13;
pub const RTMC_VEC_MAX_NUM: _bindgen_ty_5 = 14;

pub type _bindgen_ty_5 = ::std::os::raw::c_uint;

pub const RTMC_DEQ_SETTINGS_QUEUE: _bindgen_ty_6 = 0;
pub const RTMC_DEQ_MAX_NUM: _bindgen_ty_6 = 1;

pub type _bindgen_ty_6 = ::std::os::raw::c_uint;

#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct RtmRet {
    pub num: usize,
    pub s_val: *mut *mut ::std::os::raw::c_char,
    pub i_val: *mut ::std::os::raw::c_int,
    pub j_val: *mut ::std::os::raw::c_int,
}

#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct AddrRet {
    pub name: *mut ::std::os::raw::c_char,
    pub index: ::std::os::raw::c_int,
    pub type_signal: ::std::os::raw::c_int,
    pub type_value: ::std::os::raw::c_int,
    pub type_signal_desc: *mut ::std::os::raw::c_char,
    pub type_value_desc: *mut ::std::os::raw::c_char,
    pub value_min: *mut ::std::os::raw::c_char,
    pub value_max: *mut ::std::os::raw::c_char,
    pub immutable: ::std::os::raw::c_int,
}

#[derive(Debug, Default, Clone)]
pub(crate) struct Element {
    pub(crate) s: String,
    pub(crate) i: i32,
    pub(crate) j: i32,
}

#[derive(Debug, Default, Clone)]
pub(crate) struct RTMReturn {
    pub(crate) elements: Vec<Element>,
}

#[derive(Debug, Clone)]
pub struct Table {
    pub id: i32,
    pub name: String,
}

pub(crate) fn has_key<F>(f: F, id: i32, key: &str) -> i32
where
    F: Fn(i32, *const ::std::os::raw::c_char) -> i32,
{
    let c_string = CString::new(key).expect("CString conversion failed");
    f(id, c_string.as_ptr())
}

pub(crate) fn read_rtm_ret(ret_ptr: *const RtmRet) -> Option<RTMReturn> {
    return unsafe {
        if ret_ptr.is_null() {
            None
        } else {
            let ret = ptr::read(ret_ptr);
            let mut r = RTMReturn { elements: vec![] };
            let i_val_s = slice::from_raw_parts(ret.i_val, ret.num);
            let s_val_s = slice::from_raw_parts(ret.s_val, ret.num);
            for i in 0..(&ret).num {
                let c_str = CStr::from_ptr(s_val_s[i]);
                r.elements.push(Element {
                    s: c_str.to_string_lossy().into_owned(),
                    i: i_val_s[i],
                    j: 0,
                })
            }
            if !ret.j_val.is_null() {
                let j_val_s = slice::from_raw_parts(ret.i_val, ret.num);
                for i in 0..(&ret).num {
                    r.elements[i].j = j_val_s[i];
                }
            }
            Some(r)
        }
    };
}

#[derive(Debug, Clone, PartialEq)]
pub enum Value {
    UInt8(u8),
    UInt16(u16),
    UInt32(u32),
    UInt64(u64),
    Int8(i8),
    Int16(i16),
    Int32(i32),
    Int64(i64),
    Float(f32),
    Double(f64),
    String(String),
    Num(f64),
}

#[derive(Debug)]
pub struct RTMValue {
    tp: u32,
    ptr: *const std::ffi::c_void,
}

unsafe impl Send for RTMValue {}

impl RTMValue {
    pub fn new(tp: u32, ptr: *const std::ffi::c_void) -> Self {
        RTMValue { tp, ptr }
    }
    pub fn read(&self) -> Result<Value, Error> {
        new_value(self.tp, self.ptr)
    }

    pub fn is_empty(&self) -> bool {
        self.ptr.is_null()
    }
}

pub(crate) fn new_value(tp: u32, ptr: *const c_void) -> Result<Value, Error> {
    if tp > 0 && tp < RTMC_SIGNAL_TYPE_NUM {}
    match tp {
        RTMC_SIGNAL_TYPE_UINT8 => Ok(unsafe { Value::UInt8(ptr::read::<u8>(ptr as *const u8)) }),
        RTMC_SIGNAL_TYPE_UINT16 => {
            Ok(unsafe { Value::UInt16(ptr::read::<u16>(ptr as *const u16)) })
        }
        RTMC_SIGNAL_TYPE_UINT32 => {
            Ok(unsafe { Value::UInt32(ptr::read::<u32>(ptr as *const u32)) })
        }
        RTMC_SIGNAL_TYPE_UINT64 => {
            Ok(unsafe { Value::UInt64(ptr::read::<u64>(ptr as *const u64)) })
        }
        RTMC_SIGNAL_TYPE_INT8 => Ok(unsafe { Value::Int8(ptr::read::<i8>(ptr as *const i8)) }),
        RTMC_SIGNAL_TYPE_INT16 => Ok(unsafe { Value::Int16(ptr::read::<i16>(ptr as *const i16)) }),
        RTMC_SIGNAL_TYPE_INT32 => Ok(unsafe { Value::Int32(ptr::read::<i32>(ptr as *const i32)) }),
        RTMC_SIGNAL_TYPE_INT64 => Ok(unsafe { Value::Int64(ptr::read::<i64>(ptr as *const i64)) }),
        RTMC_SIGNAL_TYPE_FLOAT => Ok(unsafe { Value::Float(ptr::read::<f32>(ptr as *const f32)) }),
        RTMC_SIGNAL_TYPE_DOUBLE => {
            Ok(unsafe { Value::Double(ptr::read::<f64>(ptr as *const f64)) })
        }
        RTMC_SIGNAL_TYPE_STRING => Ok(unsafe {
            Value::String(
                CStr::from_ptr(ptr as *const c_char)
                    .to_string_lossy()
                    .into_owned(),
            )
        }),
        RTMC_SIGNAL_TYPE_NUM => Ok(unsafe { Value::Num(ptr::read::<f64>(ptr as *const f64)) }),
        _ => Err(Error::new(ErrorKind::Other, "illegal data type")),
    }
}

pub(crate) fn set_value_to_rtm(
    val: Value,
    f: impl Fn(*mut c_void, c_int, usize) -> Result<(), Error>,
) -> Result<(), Error> {
    match val {
        Value::UInt8(mut v) => {
            let ptr = &mut v as *mut u8;
            f(
                ptr as *mut c_void,
                RTMC_SIGNAL_TYPE_UINT8 as c_int,
                size_of_val(&v),
            )
        }
        Value::UInt16(mut v) => {
            let ptr = &mut v as *mut u16;
            f(
                ptr as *mut c_void,
                RTMC_SIGNAL_TYPE_UINT16 as c_int,
                size_of_val(&v),
            )
        }
        Value::UInt32(mut v) => {
            let ptr = &mut v as *mut u32;
            f(
                ptr as *mut c_void,
                RTMC_SIGNAL_TYPE_UINT32 as c_int,
                size_of_val(&v),
            )
        }
        Value::UInt64(mut v) => {
            let ptr = &mut v as *mut u64;
            f(
                ptr as *mut c_void,
                RTMC_SIGNAL_TYPE_UINT64 as c_int,
                size_of_val(&v),
            )
        }
        Value::Int8(mut v) => {
            let ptr = &mut v as *mut i8;
            f(
                ptr as *mut c_void,
                RTMC_SIGNAL_TYPE_INT8 as c_int,
                size_of_val(&v),
            )
        }
        Value::Int16(mut v) => {
            let ptr = &mut v as *mut i16;
            f(
                ptr as *mut c_void,
                RTMC_SIGNAL_TYPE_INT16 as c_int,
                size_of_val(&v),
            )
        }
        Value::Int32(mut v) => {
            let ptr = &mut v as *mut i32;
            f(
                ptr as *mut c_void,
                RTMC_SIGNAL_TYPE_INT32 as c_int,
                size_of_val(&v),
            )
        }
        Value::Int64(mut v) => {
            let ptr = &mut v as *mut i64;
            f(
                ptr as *mut c_void,
                RTMC_SIGNAL_TYPE_INT64 as c_int,
                size_of_val(&v),
            )
        }
        Value::Float(mut v) => {
            let ptr = &mut v as *mut f32;
            f(
                ptr as *mut c_void,
                RTMC_SIGNAL_TYPE_FLOAT as c_int,
                size_of_val(&v),
            )
        }
        Value::Double(mut v) => {
            let ptr = &mut v as *mut f64;
            f(
                ptr as *mut c_void,
                RTMC_SIGNAL_TYPE_DOUBLE as c_int,
                size_of_val(&v),
            )
        }
        Value::String(v) => {
            let len = v.len();
            let val_string = CString::new(v).expect("CString conversion failed");
            let val_string_ptr = val_string.as_ptr();
            f(
                val_string_ptr as *mut c_void,
                RTMC_SIGNAL_TYPE_STRING as c_int,
                len,
            )
        }
        Value::Num(mut v) => {
            let ptr = &mut v as *mut f64;
            f(
                ptr as *mut c_void,
                RTMC_SIGNAL_TYPE_NUM as c_int,
                size_of_val(&v),
            )
        }
    }
}

pub(crate) fn get_strings(r: Option<RTMReturn>) -> Vec<String> {
    let mut res = vec![];
    r.unwrap_or_else(|| RTMReturn::default())
        .elements
        .into_iter()
        .for_each(|e| {
            res.push(e.s);
        });
    res
}

pub(crate) type RTMInitT = unsafe extern "C" fn() -> ::std::os::raw::c_int;
pub(crate) type RtmSetupT = unsafe extern "C" fn(reinit: ::std::os::raw::c_int) -> ::std::os::raw::c_int;
pub(crate) type RtmInitT = unsafe extern "C" fn() -> ::std::os::raw::c_int;
pub(crate) type RtmMapGetAllIdT = unsafe extern "C" fn() -> *mut RtmRet;
pub(crate) type RtmMapSeqGetAllIdT = unsafe extern "C" fn() -> *mut RtmRet;
pub(crate) type RtmVecGetAllIdT = unsafe extern "C" fn() -> *mut RtmRet;
pub(crate) type RtmDeqGetAllIdT = unsafe extern "C" fn() -> *mut RtmRet;
pub(crate) type RtmMapGetSizeT = unsafe extern "C" fn(id: ::std::os::raw::c_int) -> usize;
pub(crate) type RtmMapSeqGetSizeT = unsafe extern "C" fn(id: ::std::os::raw::c_int) -> usize;
pub(crate) type RtmVecGetSizeT = unsafe extern "C" fn(id: ::std::os::raw::c_int) -> usize;
pub(crate) type RtmDeqGetSizeT = unsafe extern "C" fn(id: ::std::os::raw::c_int) -> usize;
pub(crate) type RtmMapHasKeyT = unsafe extern "C" fn(
    id: ::std::os::raw::c_int,
    key: *const ::std::os::raw::c_char,
) -> ::std::os::raw::c_int;
pub(crate) type RtmMapSeqHasKeyT = unsafe extern "C" fn(
    id: ::std::os::raw::c_int,
    key: *const ::std::os::raw::c_char,
) -> ::std::os::raw::c_int;
pub(crate) type RtmMapGetAllKeysT = unsafe extern "C" fn(id: ::std::os::raw::c_int) -> *mut RtmRet;
pub(crate) type RtmMapSeqGetAllKeysT = unsafe extern "C" fn(id: ::std::os::raw::c_int) -> *mut RtmRet;
pub(crate) type RtmMapGetValueT = unsafe extern "C" fn(
    id: ::std::os::raw::c_int,
    key: *const ::std::os::raw::c_char,
) -> *mut ::std::os::raw::c_void;
pub(crate) type RtmMapSeqGetValueT = unsafe extern "C" fn(
    id: ::std::os::raw::c_int,
    key: *const ::std::os::raw::c_char,
    i: ::std::os::raw::c_int,
) -> *mut ::std::os::raw::c_void;
pub(crate) type RtmMapSetValueT = unsafe extern "C" fn(
    id: ::std::os::raw::c_int,
    key: *const ::std::os::raw::c_char,
    value: *const ::std::os::raw::c_void,
    value_type: ::std::os::raw::c_int,
    value_size: usize,
) -> ::std::os::raw::c_int;
pub(crate) type RtmMapSeqSetValueT = unsafe extern "C" fn(
    id: ::std::os::raw::c_int,
    key: *const ::std::os::raw::c_char,
    i: ::std::os::raw::c_int,
    value: *const ::std::os::raw::c_void,
    value_type: ::std::os::raw::c_int,
    value_size: usize,
) -> ::std::os::raw::c_int;
pub(crate) type RtmVecGetAllValuesT = unsafe extern "C" fn(id: ::std::os::raw::c_int) -> *mut RtmRet;
pub(crate) type RtmVecGetAllValuesByNameT =
    unsafe extern "C" fn(name: *const ::std::os::raw::c_char) -> *mut RtmRet;
pub(crate) type RtmVecGetValuesT = unsafe extern "C" fn(
    id: ::std::os::raw::c_int,
    st: ::std::os::raw::c_int,
    len: ::std::os::raw::c_int,
) -> *mut RtmRet;
pub(crate) type RtmDeqGetAllValueT = unsafe extern "C" fn(id: ::std::os::raw::c_int) -> *mut RtmRet;
pub(crate) type RtmDeqGetAllValueByNameT =
    unsafe extern "C" fn(name: *const ::std::os::raw::c_char) -> *mut RtmRet;
pub(crate) type RtmDeqPopAllValueT = unsafe extern "C" fn(id: ::std::os::raw::c_int) -> *mut RtmRet;
pub(crate) type RtmDeqPopAllValueByNameT =
    unsafe extern "C" fn(name: *const ::std::os::raw::c_char) -> *mut RtmRet;
pub(crate) type RtmDeqPushBackValueT = unsafe extern "C" fn(
    id: ::std::os::raw::c_int,
    str_: *const ::std::os::raw::c_void,
    len: usize,
) -> ::std::os::raw::c_int;
pub(crate) type RtmDeqPushBackValueByNameT = unsafe extern "C" fn(
    name: *const ::std::os::raw::c_char,
    str_: *const ::std::os::raw::c_void,
    len: usize,
) -> ::std::os::raw::c_int;
pub(crate) type RtmFreeRetT = unsafe extern "C" fn(arg1: *mut RtmRet);
pub(crate) type RtmFreeAddrT = unsafe extern "C" fn(arg1: *mut AddrRet);
pub(crate) type RtmFindSignalT = unsafe extern "C" fn(addr: *const ::std::os::raw::c_char) -> *mut AddrRet;
pub(crate) type RtmFindChanT = unsafe extern "C" fn(addr: *const ::std::os::raw::c_char) -> *mut AddrRet;
pub(crate) type UpdateParaT = unsafe extern "C" fn(
    addr: *mut AddrRet,
    value: *const ::std::os::raw::c_void,
    value_size: usize,
) -> ::std::os::raw::c_int;