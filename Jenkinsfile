pipeline {
    agent {
        docker {
            image 'a40i/rust:2.0'
        }
    }
    parameters {
        booleanParam(name: 'RUN_DEPLOY', defaultValue: false, description: '是否执行部署')
        choice(name: 'SELECT_HOST', choices: ['*************', '*************','*************'], description: '选择部署环境')
        string(name: 'CUSTOM_HOST', defaultValue: '', description: '或者输入自定义值（如果输入，则优先使用）')
    }
    stages {
        stage('Fetch') {
            steps {
                echo 'Fetching code...'
                git credentialsId: 'esdd', url: '*************:njderi/opsd.git'
            }
        }
        stage('Build') {
            steps {
                echo 'Building application...'
                sh 'echo $PATH && cargo build --target=armv7-unknown-linux-gnueabihf --release'
            }
        }
        stage('Pack') {
            steps {
                echo 'Packing application...'
                sh 'chmod +x ./pack.sh && ./pack.sh opsd'
                archiveArtifacts artifacts: 'opsd*.zip,opsd*.ipk', followSymlinks: false
            }
        }
        stage('Deploy') {
            when {
                expression { env.RUN_DEPLOY}
            }
            steps {
                script {
                    def remote = [:]
                    remote.host = params.CUSTOM_HOST ? params.CUSTOM_HOST : params.SELECT_HOST
                    remote.name = 'mgc'
                    remote.user = 'root'
                    remote.password = 'deri'
                    remote.allowAnyHosts = true
                    def files = findFiles(glob: 'opsd-*.zip')
                    for (file in files) {
                        echo "Upload: ${file.path}"
                        sshPut remote: remote, from: file.path, into: '.'
                    }
                }
            }
        }
    }
    post {
        always {
            echo 'Cleaning up...'
        }
    }
}