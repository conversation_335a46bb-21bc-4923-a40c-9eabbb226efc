use super::standard::Standard;
use crate::{CardInfo, CommState, Module};
use std::io::Result;
pub(crate) struct ME3630 {
    com: Standard,
}

impl ME3630 {
    #[allow(unused)]
    pub(crate) fn new(dev: &str) -> Result<Self> {
        Ok(ME3630 {
            com: Standard::new(dev)?,
        })
    }
    pub(crate) fn new_from(s: Standard) -> Self {
        ME3630 { com: s }
    }
}

impl Module for ME3630 {
    fn info(&mut self) -> Result<crate::ModelInfo> {
        self.com.info()
    }

    fn get_card_info(&mut self) -> Result<Option<CardInfo>> {
        if self
            .com
            .at_cmd("AT+CPIN?")?
            .iter()
            .find(|e| e.contains("READY"))
            .is_some()
        {
            let mut info = CardInfo::default();
            for ele in self.com.at_cmd("AT+ZGETICCID;+COPS?")?.into_iter() {
                let trimmed = ele.trim();
                if trimmed.is_empty() {
                    continue;
                }
                let mut split = trimmed.split(":");
                match split.nth(0).unwrap() {
                    "+ZGETICCID" => {
                        info.iccid = split.nth(1).unwrap_or_default().trim().to_string();
                    }
                    "+COPS" => {
                        let cops = split.nth(1).unwrap_or_default();
                        info.operator = cops
                            .split(",")
                            .nth(2)
                            .unwrap_or_default()
                            .trim()
                            .trim_matches('"')
                            .to_string();
                    }
                    _ => todo!(),
                }
            }
            info.imsi = self.com.at_cmd_then_get_first("AT+CIMI")?;
            Ok(Some(info))
        } else {
            Ok(None)
        }
    }
    fn get_comm_state(&mut self) -> Result<CommState> {
        let mut state = CommState::default();
        state.mode = self
            .com
            .at_cmd_then_get_first("AT+ZARFCN?")?
            .split(":")
            .nth(0)
            .ok_or(std::io::Error::other("invalid mode"))?
            .to_string();
        match state.mode.as_str() {
            "LTE" => {
                let resp = self.com.at_cmd_then_get_first("AT+ZSRVRSP?")?;
                let mut values = resp.split(":").nth(1).unwrap().split(",");
                state.rsrp = values
                    .nth(0)
                    .map_or(0, |e| e.trim_matches('"').parse::<i32>().unwrap_or(0));
                state.snr = values
                    .nth(1)
                    .map_or(0, |e| e.trim_matches('"').parse::<i32>().unwrap_or(0));
            }
            _ => return Err(std::io::Error::other("unsupport mode")),
        }
        Ok(state)
    }
}
