use super::standard::Standard;
use crate::{CardInfo, CommState, Module};
use std::io::{<PERSON><PERSON><PERSON>, Result};
pub(crate) struct NL668 {
    com: Standard,
}

impl NL668 {
    #[allow(unused)]
    pub fn new(dev: &str) -> Result<Self> {
        Ok(NL668 {
            com: Standard::new(dev)?,
        })
    }
    pub(crate) fn new_from(s: Standard) -> Self {
        NL668 { com: s }
    }
}

impl Module for NL668 {
    fn info(&mut self) -> Result<crate::ModelInfo> {
        self.com.info()
    }
    fn get_card_info(&mut self) -> Result<Option<CardInfo>> {
        if self
            .com
            .at_cmd("AT+CPIN?")?
            .iter()
            .find(|e| e.contains("READY"))
            .is_some()
        {
            let mut info = CardInfo::default();
            for ele in self.com.at_cmd("AT+CCID?;+CIMI?;+COPS?")?.into_iter() {
                let trimmed = ele.trim();
                if trimmed.is_empty() {
                    continue;
                }
                let mut split = trimmed.split(":");
                match split.next().unwrap() {
                    "+CCID" => {
                        info.iccid = split.next().unwrap_or_default().trim().to_string();
                    }
                    "+CIMI" => {
                        info.imsi = split.next().unwrap_or_default().trim().to_string();
                    }
                    "+COPS" => {
                        let cops = split.next().unwrap_or_default();
                        info.operator = cops
                            .split(",")
                            .nth(2)
                            .unwrap_or_default()
                            .trim_matches('"')
                            .to_string();
                    }
                    _ => {}
                }
            }
            Ok(Some(info))
        } else {
            Ok(None)
        }
    }

    fn get_comm_state(&mut self) -> Result<CommState> {
        let resp = self.com.at_cmd("AT+GTCCINFO?")?;
        let re = regex::Regex::new(r"^(\w+) service cell:").unwrap();
        let mut state = CommState::default();
        let mut sp = resp
            .iter()
            .skip_while(|e| {
                let caps = re.captures(e.trim());
                match caps {
                    Some(cap) => {
                        state.mode = cap.get(1).unwrap().as_str().to_string();
                        false
                    }
                    None => true,
                }
            })
            .nth(1)
            .ok_or(Error::other("invalid AT resp"))?
            .split(",");
        state.snr = sp.nth(10).map_or(0, |e| e.parse::<i32>().unwrap_or(99));
        state.rsrp = sp.nth(2).map_or(0, |e| e.parse::<i32>().unwrap_or(99));
        Ok(state)
    }
}

#[cfg(test)]
mod tests {

    use crate::Module;
    #[test]
    fn test_nl668() {
        let mut port = super::NL668::new("/dev/ttyUSB1").unwrap();
        // let mut port = NL668::new("/dev/ttyUSB1").unwrap();
        println!("{:?}", port.info());
        println!("{:?}", port.get_card_info());
        println!("{:?}", port.get_comm_state());
    }
}
