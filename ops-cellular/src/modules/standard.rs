use super::port_at_command;
use crate::ModelInfo;
use std::io::Result;
use std::time::Duration;
pub(super) struct Standard {
    pub(super) port: Box<dyn serialport::SerialPort>,
}

impl Standard {
    pub fn new(dev: &str) -> Result<Self> {
        Ok(Self {
            port: serialport::new(dev, 115_200)
                .timeout(Duration::from_millis(5000))
                .open()?,
        })
    }

    pub(super) fn at_cmd(&mut self, cmd: &str) -> std::io::Result<Vec<String>> {
        port_at_command(&mut self.port, cmd)
    }

    pub(super) fn at_cmd_then_get_first(&mut self, cmd: &str) -> Result<String> {
        Ok(self
            .at_cmd(cmd)?
            .into_iter()
            .find(|e| {
                let trimmed = e.trim();
                trimmed.trim().ne(cmd) && !trimmed.is_empty()
            })
            .unwrap_or_default())
    }

    pub(super) fn info(&mut self) -> Result<ModelInfo> {
        let mut info = ModelInfo::default();
        for ele in self.at_cmd("ATI")?.into_iter() {
            let trimmed = ele.trim();
            if trimmed.is_empty() {
                continue;
            }
            let mut split = trimmed.split(":");
            match split.next().unwrap() {
                "Manufacturer" => {
                    info.manufacturer = split.next().unwrap_or_default().trim().to_string();
                }
                "Model" => {
                    info.model = split.next().unwrap_or_default().trim().to_string();
                }
                "Revision" => {
                    info.rev = split.next().unwrap_or_default().trim().to_string();
                }
                "IMEI" => {
                    info.rev = split.next().unwrap_or_default().trim().to_string();
                }
                _ => {}
            }
        }
        Ok(info)
    }
}