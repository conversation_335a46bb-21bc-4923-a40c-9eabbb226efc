mod me3630;
mod nl668;
mod standard;
use std::io::Result;
use std::io::{BufRead, Write};
use std::ops::ControlFlow;

use crate::Module;
use standard::Standard;

fn port_at_command(
    port: &mut Box<dyn serialport::SerialPort>,
    cmd: &str,
) -> std::io::Result<Vec<String>> {
    port.clear(serialport::ClearBuffer::All)?;
    port.write_all(cmd.as_bytes())?;
    port.write("\r\n".as_bytes())?;
    let mut response = vec![]; // 用于存储响应的缓冲区
    std::io::BufReader::new(&mut *port)
        .lines()
        .try_for_each(|e| match e {
            Ok(line) => {
                if line.contains("OK") {
                    ControlFlow::Break(Ok(()))
                } else if line.contains("ERROR") {
                    ControlFlow::Break(Err(std::io::Error::new(std::io::ErrorKind::Other, line)))
                } else {
                    response.push(line);
                    ControlFlow::Continue(())
                }
            }
            Err(er) => ControlFlow::Break(Err(er)),
        })
        .break_value()
        .unwrap()?;
    Ok(response)
}

pub fn new(dev: &str) -> Result<Box<dyn Module>> {
    let mut s = Standard::new(dev)?;
    match s.info()?.model.as_str() {
        "NL668-CN" => Ok(Box::new(nl668::NL668::new_from(s))),
        "ME3630-W" => Ok(Box::new(me3630::ME3630::new_from(s))),
        unkown => Err(std::io::Error::new(
            std::io::ErrorKind::Other,
            format!("unsupport model {}", unkown),
        )),
    }
}