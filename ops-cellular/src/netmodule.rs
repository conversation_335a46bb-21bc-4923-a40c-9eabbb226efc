use crate::{CardInfo, ModelInfo, Module};
use std::collections::HashMap;
use std::io::{Error, Result};
use std::os::unix::fs::PermissionsExt;
pub struct Client {}

struct NetworkModule {
    pub model: String,
    pub version: String,
    pub rsrp: i32, //信号强度
    pub sinr: i32, //信噪比
    pub mode: String,
    pub(crate) card: Option<CardInfo>,
}

impl Client {
    pub fn try_new() -> Result<Self> {
        if std::fs::metadata("/usr/bin/netmodule")
            .or(std::fs::metadata("/usr/local/bin/netmodule"))?
            .permissions()
            .mode()
            & 0o111
            != 0
        {
            Ok(Self {})
        } else {
            Err(Error::other("netmodule not found"))
        }
    }

    pub fn run_command(mut f: impl FnMut(&str, &str) -> anyhow::Result<()>) -> anyhow::Result<()> {
        let out = std::process::Command::new("netmodule")
            .args(vec!["info"])
            .output()?;
        if !out.status.success() {
            return Err(anyhow::anyhow!(String::from_utf8_lossy(&out.stderr)
                .trim()
                .to_string()));
        };
        let out_str = String::from_utf8_lossy(&out.stdout);
        for line in out_str.lines() {
            let mut split = line.trim().splitn(2, ":");
            f(
                split.next().unwrap_or_default().trim(),
                split.next().unwrap_or_default().trim(),
            )?;
        }
        Ok(())
    }

    fn run() -> Result<NetworkModule> {
        let mut kv = HashMap::new();
        Self::run_command(|k, v| {
            kv.insert(k.to_string(), v.to_string());
            Ok(())
        })
        .map_err(|e| Error::other(e.to_string()))?;
        let mut module = NetworkModule {
            model: kv["model"].to_string(),
            rsrp: kv.get("rsrp").map_or(0, |e| e.parse::<i32>().unwrap_or(0)),
            sinr: kv.get("sinr").map_or(0, |e| e.parse::<i32>().unwrap_or(0)),
            version: kv
                .get("rev")
                .map_or("UNKNOWN".to_string(), |e| e.to_string()),
            mode: kv.get("mode").map_or("".to_string(), |e| e.to_string()),
            card: None,
        };

        if let Some(v) = kv.get("cd") {
            if v.parse::<i32>().unwrap_or(0) > 0 {
                module.card = Some(CardInfo {
                    iccid: kv.get("iccid").map_or("".to_string(), |e| e.to_string()),
                    imsi: kv.get("imsi").map_or("".to_string(), |e| e.to_string()),
                    operator: kv.get("oper").map_or("".to_string(), |e| e.to_string()),
                });
            }
        }
        Ok(module)
    }
}

impl Module for Client {
    fn info(&mut self) -> Result<ModelInfo> {
        let r = Self::run()?;
        Ok(ModelInfo {
            model: r.model,
            rev: r.version,
            ..Default::default()
        })
    }

    fn get_card_info(&mut self) -> Result<Option<crate::CardInfo>> {
        Ok(Self::run()?.card)
    }

    fn get_comm_state(&mut self) -> Result<crate::CommState> {
        let r = Self::run()?;
        Ok(crate::CommState {
            mode: r.mode,
            rsrp: r.rsrp,
            snr: r.sinr,
        })
    }
}
