use std::io::Result;
pub mod modules;
pub mod netmodule;
#[derive(De<PERSON>ult, Debug,Clone)]
pub struct CardInfo {
    pub iccid: String,
    pub imsi: String,
    pub operator: String,
}

#[derive(Default, Debug)]
pub struct ModelInfo {
    pub model: String,
    pub manufacturer: String,
    pub rev: String,
    pub imei: String,
}

#[derive(Default, Debug)]
pub struct CommState {
    pub mode: String,
    pub rsrp: i32,
    pub snr: i32,
}

pub trait Module {
    fn info(&mut self) -> Result<ModelInfo>;
    fn get_card_info(&mut self) -> Result<Option<CardInfo>>;
    fn get_comm_state(&mut self) -> Result<CommState>;
}

pub fn new_valid_module(dev: &str) -> Result<Box<dyn Module>> {
    match crate::netmodule::Client::try_new() {
        Ok(cli) => {
            // let c: Box<dyn Module> = Box::new(cli);
            Ok(Box::new(cli))
        }
        Err(_) => modules::new(dev),
    }
}
