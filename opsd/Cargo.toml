[package]
name = "opsd"
version = "1.1.0"
edition = "2021"

[dependencies]
trait-variant = "0.1.2"
tokio = { version = "1.41", features = ["full","tracing"] }
rumqttc = { version = "0.24" }
log = { version = "0.4", features = ["max_level_debug", "release_max_level_info"] }
env_logger = "0.11.3"
anyhow = "1.0.82"
lazy_static = "1.4.0"
sysinfo = { version = "0.33.0" }
clap = { version = "4", features = ["cargo", "derive"] }
zip = { version = "2" }
daemonize = { version = "0.5" }
regex = { version = "1" }
syslog = { version = "7",optional = true}
base64 = "0.21.7"
uuid = "1"
reqwest = { version = "0.12", features = ["multipart","blocking", "stream", "json"] }
bytes = { version = "1" }
prost = "0.13"
tokio-retry = "0.3"
num_enum = "0.7.2"
psutil = "3"
libc = "0.2"
sha2 = "0.10.8"
toml = "0.8"
http = "1.1.0"
serde = { version = "1.0", features = ["derive"] }
async-trait = "0.1.81"
hex = "0.4.3"
derive_more = { version = "1.0.0", features = ["full"] }
futures-util = "0.3.30"
serde_json = "1.0.114"
procfs = "0.17.0"
ops-cellular = {path = "../ops-cellular"}
tempfile = "3.20.0"
[build-dependencies]
prost-build = "0.13"
[features]
default = ["slog"]
nomgmodel = ["slog"]
slog = ["syslog"]
