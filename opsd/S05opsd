#!/bin/bash
BIN_PATH=/usr/local/bin/opsd
[ -x ${BIN_PATH} ] || chmod +x ${BIN_PATH}
start() {
        printf "Starting opsd: "
        ${BIN_PATH} start
        [ $? = 139 ] && echo "OK" || echo "FAIL"
}
stop() {
        printf "Stopping opsd: "
        ${BIN_PATH} stop
        [ $? = 0 ] && echo "OK" || echo "FAIL"
}
restart() {
        stop
        start
}

case "$1" in
start)
        start
        ;;
stop)
        stop
        ;;
restart | reload)
        restart
        ;;
*)
        start
        #        echo "Usage: $0 {start|stop|restart}"
        exit 1
        ;;
esac
