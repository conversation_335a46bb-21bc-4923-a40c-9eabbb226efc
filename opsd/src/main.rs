use std::{io::Error<PERSON><PERSON>, path::Path};

use clap::{arg, Arg, Command};
use daemonize::Daemonize;

#[cfg(feature = "slog")]
extern crate syslog;
use log::LevelFilter;
use syslog::Facility;
mod app;
mod cloud;
mod ota;
mod system;

const PID_FILE: &str = "/var/run/opsd.pid";
const OPS_STOP_IF_RUNNING: &str = "OPS_STOP_IF_RUNNING";
fn start(force: bool) -> anyhow::Result<()> {
    if force {
        stop()?;
    }
    let daemonize = Daemonize::new().pid_file(PID_FILE).umask(0o000);
    match daemonize.start() {
        Ok(_) => {
            #[cfg(feature = "slog")]
            syslog::init(Facility::LOG_USER, LevelFilter::Info, None)
                .map_err(|e| std::io::Error::new(ErrorKind::Other, e.to_string()))?;
            crate::cloud::manage::serve();
            Ok(())
        }
        Err(e) => {
            println!("start failed {}", e.to_string());
            std::process::exit(1);
        }
    }
}

fn stop() -> anyhow::Result<()> {
    let p = Path::new(PID_FILE);
    if p.metadata().is_ok() {
        let ret = std::fs::read_to_string(Path::new(PID_FILE))?;
        if !ret.is_empty() {
            let r = std::process::Command::new("kill")
                .arg(ret.trim())
                .output()?;
            if !r.status.success() {
                print!("{}", String::from_utf8_lossy(&r.stderr));
            } else {
                _ = std::fs::remove_file(PID_FILE);
            }
        }
    }
    Ok(())
}

fn main() -> anyhow::Result<()> {
    let cmd = clap::command!()
        .about("DERI ")
        .subcommand(
            Command::new("start")
                .about("start as daemon")
                .arg(arg!(-p --pid <PID>"old process pid")),
        )
        .subcommand(
            Command::new("deploy")
                .about("deploy standard application")
                .arg(
                    Arg::new("path")
                        .index(1)
                        .help("deploy package path")
                        .required(true),
                ),
        )
        .subcommand(Command::new("stop").about("stop daemon"));

    let matches = cmd.clone().get_matches();
    if let Some((name, _)) = matches.subcommand() {
        match name {
            "start" => {
                let force = std::env::vars()
                    .find(|e| {
                        if e.0.eq(OPS_STOP_IF_RUNNING) {
                            true
                        } else {
                            false
                        }
                    })
                    .is_some();
                start(force)
            }
            "stop" => stop(),
            _ => Ok(()),
        }
    } else {
        // syslog::init(Facility::LOG_USER, LevelFilter::Info, None)
        //     .map_err(|e| std::io::Error::new(ErrorKind::Other, e.to_string()))?;
        env_logger::init();
        cloud::manage::serve();
        Ok(())
    }
}
