/*
 * Copyright (c) 2024. DERI. Nanjing, China. All rights reserved
 * FILENAME: ota.rs
 * PROJECT: opsd
 * AUTHOR: shenshuangquan
 */

pub mod executor;
pub mod resource;
pub mod task;

pub const OTA_TMP_DATA_PATH: &str = "/tmp/ota";

#[async_trait::async_trait]
pub(crate) trait Executor {
    async fn upgrade(
        &self,
        path: &std::path::Path,
        logger: tokio::sync::mpsc::Sender<String>,
    ) -> anyhow::Result<()>;
}
