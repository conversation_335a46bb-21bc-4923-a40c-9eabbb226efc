/*
 * Copyright (c) 2024. DERI. Nanjing, China. All rights reserved
 * FILENAME: downloader.rs
 * PROJECT: opsd
 * AUTHOR: shenshuangquan
 */

use anyhow::anyhow;
use async_trait::async_trait;
use base64::Engine;
use futures_util::StreamExt;
use reqwest::header::HeaderValue;
use sha2::Digest;
use tokio::fs::File;
use tokio::io::AsyncWriteExt;

#[derive(serde::Deserialize, serde::Serialize,Debug, Clone)]
pub struct Authorization {
    pub username: String,
    pub password: String,
}

impl Authorization {
    pub fn new<T: Into<String>, P: Into<String>>(user: T, pass: P) -> Self {
        Authorization {
            username: user.into(),
            password: pass.into(),
        }
    }
}

impl TryFrom<&Authorization> for HeaderValue {
    type Error = http::Error;

    fn try_from(value: &Authorization) -> Result<Self, Self::Error> {
        let encoded = base64::engine::general_purpose::URL_SAFE
            .encode(format!("{}:{}", value.username, value.password));
        Ok(reqwest::header::HeaderValue::from_str(&format!(
            "Basic {}",
            encoded
        ))?)
    }
}

pub struct HTTPResource {
    url: String,
    auth: Option<Authorization>,
    sha256: Option<String>,
}

#[async_trait]
pub trait Resource {
    async fn download(&self, path: &mut File) -> anyhow::Result<()>;
}

impl HTTPResource {
    pub fn new_with_auth<T: Into<String>>(url: T, authorization: Authorization) -> Self {
        HTTPResource {
            url: url.into(),
            auth: Some(authorization),
            sha256: None,
        }
    }

    #[allow(unused)]
    pub fn new<T: Into<String>>(url: T) -> Self {
        HTTPResource {
            url: url.into(),
            auth: None,
            sha256: None,
        }
    }

    pub fn with_sha256<T: Into<String>>(mut self, sha256: T) -> Self {
        self.sha256 = Some(sha256.into());
        self
    }
}

#[async_trait]
impl Resource for HTTPResource {
    async fn download(&self, file: &mut File) -> anyhow::Result<()> {
        let client = reqwest::Client::builder()
            .danger_accept_invalid_certs(true) // 跳过 SSL 校验
            .build()?;
        let mut resp_builder = client.get(&self.url);
        if let Some(v) = &self.auth {
            resp_builder = resp_builder.header(reqwest::header::AUTHORIZATION, v);
        }
        let resp = resp_builder.send().await?;
        if resp.status() != 200 {
            return Err(anyhow!("request failed,code = {}", resp.status().as_str()));
        }
        let mut hasher = sha2::Sha256::new();
        let mut stream = resp.bytes_stream();
        while let Some(item) = stream.next().await {
            let chunk = item?;
            hasher.update(&chunk);
            file.write_all(&chunk).await?;
        }

        let hash = hasher.finalize();
        if let Some(sha256) = &self.sha256 {
            if !format!("{:x}", hash).eq(sha256) {
                return Err(anyhow!("check hash failed {} != {}", format!("{:x}", hash), sha256));
            }
        }
        Ok(())
    }
}
