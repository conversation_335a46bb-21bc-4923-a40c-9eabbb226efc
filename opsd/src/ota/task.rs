use std::sync::Arc;

use crate::{
    cloud::manage::Event,
    ota::{resource::Resource, Executor},
};
use tempfile::TempDir;
use tokio::{sync::Mutex, task::Jo<PERSON><PERSON><PERSON><PERSON>};
use zip::ZipArchive;

#[derive(Clone, Debug, PartialEq, Eq)]
pub(crate) enum Status {
    Downloading(u32),
    Upgrading(u32),
    Finished(Option<String>),
}

pub(crate) struct TaskHandle {
    pub(crate) handle: JoinHandle<()>,
}

pub(crate) struct Task {
    id: String,
    status: Arc<Mutex<Status>>,
    executor: Arc<dyn Executor + Send + Sync>,        //执行
    downloader: Box<dyn Resource + Send>,             //下载
    trace: Box<dyn std::io::Write + Send + Sync>,     //日志
    sender: Option<tokio::sync::mpsc::Sender<Event>>, //事件
}

pub fn new_task(
    id: String,
    executor: Arc<dyn Executor + Send + Sync>,
    downloader: Box<dyn Resource + Send>,
    trace: Box<dyn std::io::Write + Send + Sync>,
    sender: Option<tokio::sync::mpsc::Sender<Event>>,
) -> Task {
    Task {
        id,
        status: Arc::new(Mutex::new(Status::Downloading(0))),
        executor,
        downloader,
        trace,
        sender: sender,
    }
}

impl Task {
    pub(crate) async fn spawn(mut self) -> TaskHandle {
        let handle = tokio::spawn(async move {
            match self.run().await {
                Ok(_) => {
                    self.set_status(Status::Finished(None)).await;
                }
                Err(e) => {
                    log::warn!("upgrade failed error = {}", e.to_string());
                    self.set_status(Status::Finished(Some(e.to_string()))).await;
                }
            }
        });
        TaskHandle { handle }
    }

    async fn set_status(&mut self, status: Status) {
        *self.status.lock().await = status.clone();
        if let Some(sender) = self.sender.as_ref() {
            _ = sender
                .send(Event::TaskStatusChanged(self.id.clone(), status))
                .await;
        }
    }

    async fn run(&mut self) -> anyhow::Result<()> {
        let mut file = tokio::fs::File::from_std(tempfile::tempfile()?);
        //download
        writeln!(self.trace, "start download")?;
        self.set_status(Status::Downloading(0)).await;
        if let Err(e) = self.downloader.download(&mut file).await {
            writeln!(self.trace, "download failed,error = {}", e.to_string())?;
            return Err(e);
        } else {
            writeln!(self.trace, "download ok")?;
        }

        //extract to tmp dir
        let mut archive = ZipArchive::new(file.into_std().await)?;
        let tmp_dir = tokio::task::spawn_blocking(move || {
            let tmp_dir = tempfile::tempdir()?;
            archive.extract(&tmp_dir)?;
            Ok::<TempDir, anyhow::Error>(tmp_dir)
        })
        .await??;

        //spawn a new task to upgrade
        self.set_status(Status::Upgrading(0)).await;
        let (tx, mut rx) = tokio::sync::mpsc::channel::<String>(1);
        let executor = self.executor.clone();
        let handle = tokio::spawn(async move {
            executor.upgrade(tmp_dir.path(), tx).await?;
            Ok::<_, anyhow::Error>(())
        });
        while let Some(line) = rx.recv().await {
            writeln!(self.trace, "{}", line)?;
        }
        handle.await??;
        log::debug!("upgrade ok ");
        Ok(())
    }
}
