/*
 * Copyright (c) 2024. DERI. Nanjing, China. All rights reserved
 * FILENAME: executor.rs
 * PROJECT: opsd
 * AUTHOR: shenshuangquan
 */

pub(crate) mod universal {
    use std::path::Path;
    use std::time::Duration;

    use crate::ota::Executor;
    use anyhow::anyhow;
    use tokio::{
        io::{AsyncBufReadExt as _, BufReader},
        sync::mpsc::Sender,
    };
    pub struct UniversalOTAExecutor {}

    #[async_trait::async_trait]
    impl Executor for UniversalOTAExecutor {
        async fn upgrade(
            &self,
            path: &std::path::Path,
            sender: Sender<String>,
        ) -> anyhow::Result<()> {
            InstallSession::new(path.as_ref(), sender)?
                .install()
                .await?;
            Ok(())
        }
    }

    struct InstallSession<'a> {
        tmp_dir: &'a Path,
        sender: tokio::sync::mpsc::Sender<String>,
    }

    impl<'a> InstallSession<'a> {
        pub fn new(
            tmp_dir: &'a Path,
            sender: tokio::sync::mpsc::Sender<String>,
        ) -> anyhow::Result<Self> {
            Ok(Self { sender, tmp_dir })
        }

        pub async fn install(&mut self) -> anyhow::Result<()> {
            self.try_run_script("pre-deploy.sh", Duration::from_secs(300))
                .await?;
            self.try_run_script("deploy.sh", Duration::from_secs(300))
                .await?;
            self.try_run_script("post-deploy.sh", Duration::from_secs(300))
                .await?;
            Ok(())
        }

        async fn try_run_script(&mut self, script: &str, duration: Duration) -> anyhow::Result<()> {
            if !self.tmp_dir.join(script).exists() {
                return Ok(());
            }
            let mut child = tokio::process::Command::new("sh")
                .current_dir(self.tmp_dir)
                .arg(script) // 示例命令
                .stdout(std::process::Stdio::piped()) // 必须设置管道
                .stderr(std::process::Stdio::piped()) // 可选：捕获 stderr
                .spawn()?;

            // 获取 stdout/stderr 的异步读取器
            let stdout = child.stdout.take().unwrap();
            let stderr = child.stderr.take().unwrap();

            let mut stdout_reader = BufReader::new(stdout).lines();
            let mut stderr_reader = BufReader::new(stderr).lines();

            // 使用 select! 同时监听多个事件
            tokio::select! {
                // 处理 stdout
                _ = async {
                    while let Some(line) = stdout_reader.next_line().await? {
                        _ = self.sender.send(line).await;
                    }
                    Ok::<_, std::io::Error>(())
                } => {},

                // 处理 stderr
                _ = async {
                    while let Some(line) = stderr_reader.next_line().await? {
                        _ = self.sender.send(line).await;
                    }
                    Ok::<_, std::io::Error>(())
                } => {},

                // 超时控制
                _ = tokio::time::sleep(duration) => {
                    println!("Timeout reached after {:?}", duration);
                    child.kill().await?; // 终止子进程
                    return Err(anyhow!("run script {} timeout", script));
                },

                // 检查子进程是否退出
                status = child.wait() => {
                    match status {
                        Ok(status) => {
                            if !status.success() {
                                return Err(anyhow!("run script {} failed", script));
                            }
                        },
                        Err(e) => {
                            return Err(anyhow!("wait script {} failed: {}", script, e));
                        },
                    }
                }
            }

            Ok(())
        }
    }
}

pub mod ops {
    use crate::{ota, OPS_STOP_IF_RUNNING};
    use log::info;
    use std::time::Duration;
    use tokio::sync::mpsc::Sender;
    pub(crate) struct OTAExecutor {}

    #[async_trait::async_trait]
    impl ota::Executor for OTAExecutor {
        async fn upgrade(
            &self,
            path: &std::path::Path,
            logger: Sender<String>,
        ) -> anyhow::Result<()> {
            ota::executor::universal::UniversalOTAExecutor {}
                .upgrade(path, logger)
                .await?;
            info!("upgrade pkg install ok");
            //launch child process and wait for kill
            reboot_self()?;
            std::thread::sleep(Duration::from_secs(5));
            Ok(())
        }
    }

    pub(crate) fn reboot_self() -> anyhow::Result<()> {
        //launch child process and wait for kill
        let args: Vec<String> = std::env::args().collect();
        std::mem::forget(std::thread::spawn(move || {
            let mut cmd = std::process::Command::new(&args[0]);
            cmd.envs(std::env::vars()).env(OPS_STOP_IF_RUNNING, "true");
            if args.len() > 1 {
                cmd.args(&args[1..]);
            }
            match cmd.spawn() {
                Ok(child) => {
                    info!("new process[{}] spawn ok,and it will kill this", child.id());
                    std::thread::sleep(Duration::from_secs(5));
                }
                Err(_) => {
                    info!("new process spawn failed");
                }
            }
        }));
        Ok(())
    }
}
