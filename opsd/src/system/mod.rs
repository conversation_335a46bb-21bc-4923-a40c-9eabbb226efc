/*
 * Copyright (c) 2024. DERI. Nanjing, China. All rights reserved
 * FILENAME: mod.rs
 * PROJECT: opsd
 * AUTHOR: shenshuangquan
 */

use anyhow::anyhow;

pub mod network;
pub use tools::Hardware;
pub(crate) mod tools;

pub fn sn_get() -> Result<String, anyhow::Error> {
    let out = std::process::Command::new("sn").arg("get").output()?;
    if out.status.success() {
        Ok(String::from_utf8_lossy(&out.stdout).trim().to_string())
    } else {
        Err(anyhow::format_err!(String::from_utf8_lossy(&out.stderr)
            .trim()
            .to_string()))
    }
}

pub fn get_os_soft_version() -> Result<(String, String), anyhow::Error> {
    let output = std::process::Command::new("head")
        .args(["-n", "1", "/etc/rootfs.version"])
        .output()?;
    if !output.status.success() {
        return Err(anyhow!("{}", String::from_utf8_lossy(&output.stderr)));
    }
    let output = String::from_utf8_lossy(&output.stdout);
    let splits: Vec<&str> = output.trim().split_whitespace().collect();
    if splits.len() >= 2 {
        Ok((splits[0].to_string(), splits[1].to_string()))
    } else {
        Ok(("unknown".to_string(), "unknown".to_string()))
    }
}
