/*
 * Copyright (c) 2024. DERI. Nanjing, China. All rights reserved
 * FILENAME: tools.rs
 * PROJECT: opsd
 * AUTHOR: shenshuangquan
 */
use anyhow::anyhow;
use std::ffi::OsStr;
use std::ops::Sub;
use std::path::{Path, PathBuf};

const SYS_TOOL_PATH: &str = "/usr/local/libexec";

pub fn get(name: &str) -> Option<Command> {
    let p = Path::new(SYS_TOOL_PATH).join(name);
    if p.exists() {
        Some(Command { path: p })
    } else {
        None
    }
}

pub(crate) struct Command {
    path: PathBuf,
}

impl Command {
    #[allow(unused)]
    pub(crate) fn run<I, S>(&self, args: I) -> anyhow::Result<()>
    where
        I: IntoIterator<Item = S>,
        S: AsRef<OsStr>,
    {
        let ret = std::process::Command::new(&self.path).args(args).output()?;
        if ret.status.success() {
            Ok(())
        } else {
            Err(anyhow!(String::from_utf8(ret.stderr)?))
        }
    }

    pub(crate) async fn run_async<I, S>(&self, args: I) -> anyhow::Result<()>
    where
        I: IntoIterator<Item = S>,
        S: AsRef<OsStr>,
    {
        let ret = tokio::process::Command::new(&self.path)
            .args(args)
            .output()
            .await?;
        if ret.status.success() {
            Ok(())
        } else {
            Err(anyhow!(String::from_utf8(ret.stderr)?))
        }
    }
}

const VPN_PID_FILE: &str = "/var/run/edge-vpn.pid";
const VPN_DAEMON_PID_FILE: &str = "/var/run/edge-vpn-daemon.pid";

pub(crate) async fn restart_vpn_v2_0() -> anyhow::Result<()> {
    let vpn_pid_path = std::path::Path::new(VPN_PID_FILE);
    if vpn_pid_path.exists() {
        if let Some(pid) = std::fs::read_to_string(VPN_PID_FILE)?
            .trim()
            .parse::<u32>()
            .ok()
        {
            if let Some(pro) = psutil::process::Process::new(pid).ok() {
                pro.kill()?;
            }
        }
    }

    let vpn_daemon_path = std::path::Path::new(VPN_DAEMON_PID_FILE);
    if vpn_daemon_path.exists() {
        if let Some(pro) = psutil::process::Process::new(
            std::fs::read_to_string(vpn_daemon_path)?
                .trim()
                .parse::<u32>()
                .unwrap_or_default(),
        )
        .ok()
        {
            pro.kill()?;
            let mut timeout = std::time::Duration::from_secs(5);
            let step = std::time::Duration::from_millis(100);
            let mut interval = tokio::time::interval(step);
            loop {
                interval.tick().await;
                if pro.is_running() {
                    if timeout.lt(&step) {
                        return Err(anyhow!("stop vpn timed out"));
                    } else {
                        timeout = timeout.sub(step);
                    }
                } else {
                    break;
                }
            }
        }
    }

    let output = std::process::Command::new("sh")
        .args(["-c", "nohup /usr/bin/edge-vpn > /dev/null 2>&1 &"])
        .output()?;
    if !output.status.success() {
        return Err(anyhow!(String::from_utf8(output.stderr)?));
    }
    Ok(())
}

pub(crate) async fn restart_web_v2_0() -> anyhow::Result<()> {
    let child = tokio::process::Command::new("/deri/web/start_web.sh").spawn()?;
    let result = child.wait_with_output().await?;
    if result.status.success() {
        Ok(())
    } else {
        Err(anyhow!(String::from_utf8(result.stderr)?))
    }
}

#[derive(Debug, Default)]
pub struct Hardware {
    pub soc: String,
    pub board: String,
    pub chip_id: String,
}

impl Hardware {
    #[allow(unused)]
    pub fn get() -> Result<Self, anyhow::Error> {
        let out = std::process::Command::new("hwinfo").output()?;
        if !out.status.success() {
            return Err(anyhow::format_err!(String::from_utf8_lossy(&out.stderr)
                .trim()
                .to_string()));
        }
        let mut hd = Hardware::default();
        String::from_utf8_lossy(&out.stdout).lines().for_each(|e| {
            let splits: Vec<&str> = e.splitn(2, ":").collect();
            if splits.len() == 2 {
                match splits[0].trim() {
                    "cpu_serial" => {
                        hd.chip_id = splits[1].trim().to_string();
                    }
                    "board" => {
                        hd.board = splits[1].trim().to_string();
                    }
                    "core" => {
                        hd.soc = splits[1].trim().to_string();
                    }
                    _ => {}
                }
            }
        });
        Ok(hd)
    }
}

///
/// 解析以下输出：
/// Forlinx EMMC Health Check Tool
// Capacity                  : 7456 ,(unit=1M)
// Average erase count       : [ 105 / 3000 ]
// Host total write          : 141.000000 ,(unit=1G)
// Host total read           : 4.000000 ,(unit=1G)
// Bad block count           : 0
// Write amplification       : 4.879987
// Disk write capacity       : 4028.571429, (unit=1G)
// last check interval write : 0.000000, (unit=1G)
// Check statistics          : [ 1 / 7 ]
#[derive(Debug, Default, Clone)]
pub struct MMCHealth {
    pub erase_count: (u32, u32),
    pub bad_block_count: u32,
    pub write_amplification: f32,
    pub disk_write_capacity: f32,
    pub last_check_interval_write: f32,
}

pub fn read_mmc_health_info<P: AsRef<Path>>(path: P) -> Result<MMCHealth, anyhow::Error> {
    let mut health = MMCHealth::default();
    for e in std::fs::read_to_string(path.as_ref())?.lines() {
        let splits: Vec<&str> = e.splitn(2, ":").collect();
        if splits.len() == 2 {
            match splits[0].trim() {
                "Average erase count" => {
                    let count_and_max = splits[1]
                        .trim()
                        .trim_start_matches("[")
                        .trim_end_matches("]");
                    let mut splits = count_and_max.splitn(2, "/");
                    let count = splits.next().unwrap_or_default().trim();
                    let max = splits.next().unwrap_or_default().trim();
                    health.erase_count = (
                        count.parse().unwrap_or_default(),
                        max.parse().unwrap_or_default(),
                    );
                }
                "Bad block count" => {
                    health.bad_block_count = splits[1].trim().parse().unwrap_or_default();
                }
                "Write amplification" => {
                    health.write_amplification = splits[1].trim().parse().unwrap_or_default();
                }
                "Disk write capacity" => {
                    health.disk_write_capacity = splits[1]
                        .trim()
                        .splitn(2, ",")
                        .next()
                        .unwrap_or_default()
                        .trim()
                        .parse()
                        .unwrap_or_default();
                }
                "last check interval write" => {
                    health.last_check_interval_write = splits[1]
                        .trim()
                        .splitn(2, ",")
                        .next()
                        .unwrap_or_default()
                        .trim()
                        .parse()
                        .unwrap_or_default();
                }
                _ => {}
            }
        }
    }

    Ok(health)
}

pub const MMC_HEALTH_INFO_PATH: &str = "/var/mmc_guard/health.info";

pub fn run_mmcinfo() -> Result<MMCHealth, anyhow::Error> {
    std::process::Command::new("mmcinfo")
        .arg("/dev/mmcblk0")
        .output()?;
    Ok(read_mmc_health_info(MMC_HEALTH_INFO_PATH)?)
}

pub fn get_mmcinfo(interval: std::time::Duration) -> Result<MMCHealth, anyhow::Error> {
    if match std::fs::metadata(MMC_HEALTH_INFO_PATH) {
        Ok(meta) => {
            let modify_time = meta.modified()?;
            let now = std::time::SystemTime::now();
            now.duration_since(modify_time)? >= interval
        }
        Err(_) => true,
    } {
        run_mmcinfo()
    } else {
        read_mmc_health_info(MMC_HEALTH_INFO_PATH)
    }
}
