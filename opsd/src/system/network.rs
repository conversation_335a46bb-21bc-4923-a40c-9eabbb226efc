/*
 * Copyright (c) 2024. DERI. Nanjing, China. All rights reserved
 * FILENAME: network.rs
 * PROJECT: opsd
 * AUTHOR: shenshuangquan
 */

use anyhow::anyhow;
use ops_cellular as cellular;
#[derive(Debug, <PERSON><PERSON><PERSON>, <PERSON>lone)]
pub struct CelluarMetrics {
    pub model: String,
    pub rev: String,
    pub rsrp: i32, //信号强度
    pub sinr: i32, //信噪比
    pub mode: String,
    // rssi: f32,
    pub(crate) card: Option<cellular::CardInfo>,
}

pub fn cellular_info() -> anyhow::Result<CelluarMetrics> {
    let mut module = cellular::new_valid_module("/dev/ttyUSB1")?;
    let com_state = module.get_comm_state()?;
    let module_info = module.info()?;
    Ok(CelluarMetrics {
        model: module_info.model,
        rev: module_info.rev,
        rsrp: com_state.rsrp,
        sinr: com_state.snr,
        mode: com_state.mode,
        card: module.get_card_info().unwrap_or_default(),
    })
}

pub fn get_position() -> anyhow::Result<(f64, f64)> {
    let mut ret = (0.0_f64, 0.0_f64);
    cellular::netmodule::Client::run_command(|k, v| {
        if k == "latitude" {
            ret.0 = v.trim_end_matches("N").parse::<f64>()?;
        } else if k == "longitude" {
            ret.1 = v.trim_end_matches("E").parse::<f64>()?;
        }
        Ok(())
    })?;
    Ok(ret)
}

pub fn get_wlan_mac() -> anyhow::Result<String> {
    let name = get_default_net_interface()?;
    Ok(get_mac_by_net_interface(&name)?)
}

pub fn get_mac_by_net_interface(name: &str) -> anyhow::Result<String> {
    let out = std::process::Command::new("ip")
        .args(vec!["link", "show"])
        .arg(name)
        .output()?;
    if !out.status.success() {
        return Err(anyhow::format_err!(String::from_utf8_lossy(&out.stderr)
            .trim()
            .to_string()));
    };
    let out_str = String::from_utf8_lossy(&out.stdout);
    for line in out_str.lines() {
        let line = line.trim();
        if line.starts_with("link/ether") {
            let splits: Vec<&str> = line.split_whitespace().collect();
            if splits.len() > 2 {
                return Ok(splits[1].to_string());
            }
        }
    }
    Err(anyhow::anyhow!("no mac found"))
}

pub fn get_default_net_interface() -> Result<String, anyhow::Error> {
    let output = std::process::Command::new("ip")
        .args(["route", "show", "default"])
        .output()?;
    if !output.status.success() {
        return Err(anyhow!("{}", String::from_utf8_lossy(&output.stderr)));
    }
    let mut ifaces: Vec<_> = String::from_utf8_lossy(&output.stdout)
        .lines()
        .filter_map(|e| {
            let splits: Vec<_> = e.split_whitespace().collect();
            splits
                .iter()
                .position(|&f| f == "dev")
                .map_or(None, |pos| Some(splits[pos + 1].to_string()))
                .and_then(|e| {
                    Some((
                        e,
                        splits
                            .iter()
                            .position(|&f| f == "metric")
                            .map_or(0 as i32, |pos| splits[pos + 1].parse::<i32>().unwrap()),
                    ))
                })
        })
        .collect();
    ifaces.sort_by(|e, f| e.1.cmp(&f.1));
    Ok(ifaces.into_iter().next().map_or("".to_string(), |e| e.0))
}