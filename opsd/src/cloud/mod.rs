/*
 * Copyright (c) 2024. DERI. Nanjing, China. All rights reserved
 * FILENAME: opscloud.rs
 * PROJECT: opsd
 * AUTHOR: shenshuangquan
 */

use std::collections::HashMap;

pub use crate::cloud::mqtt::MQTT;
use crate::cloud::proto::mgcops::Device;
mod codec;
pub(crate) mod manage;
mod mgapp;
mod mqtt;
mod ota;
pub(crate) mod proto;
pub(crate) mod root;
pub(super) mod settings;

#[derive(Clone, Debug)]
pub(crate) struct Program {
    pub(crate) name: String,
    pub(crate) version: Option<String>,
    pub(crate) sha256: String,
}

impl Program {
    pub fn new(name: &str) -> Self {
        Self {
            name: name.to_string(),
            version: None,
            sha256: "".to_string(),
        }
    }
}
impl Into<Device> for Program {
    fn into(self) -> Device {
        Device {
            dev_type: "Program".to_string(),
            r#virtual: false,
            index: self.name.clone(),
            version: self.version.unwrap_or_default(),
            model: self.name.to_string(),
            vendor: proto::DEFAULT_VENDOR.to_string(),
            props: HashMap::from([("sha256".into(), self.sha256)]),
            ..Default::default()
        }
    }
}

pub(crate) struct Plugin {
    pub(crate) name: String,
    pub(crate) version: String,
    pub(crate) sha256: String,
}

impl Into<Device> for Plugin {
    fn into(self) -> Device {
        Device {
            dev_type: "Plugin".to_string(),
            r#virtual: false,
            index: self.name.clone(),
            version: self.version,
            props: HashMap::from([("sha256".into(), self.sha256)]),
            model: self.name,
            vendor: proto::DEFAULT_VENDOR.to_string(),
            ..Default::default()
        }
    }
}

pub(crate) fn get_request_client() -> anyhow::Result<reqwest::Client> {
    return Ok(reqwest::Client::builder()
        .danger_accept_invalid_certs(true) // 跳过 SSL 校验
        .build()?);
}
