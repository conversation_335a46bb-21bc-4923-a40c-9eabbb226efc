use std::collections::HashMap;
use std::fs::OpenOptions;
use std::io::Write;
use std::sync::Arc;
use std::time::{Duration, SystemTime, UNIX_EPOCH};

use anyhow::anyhow;
use hex::ToHex;
use log::{debug, info, warn};
use prost::Message;
use serde::{Deserialize, Serialize};
use sha2::Digest;
use tokio::sync::mpsc::{Receiver, Sender};
use tokio::sync::Mutex;
use tokio_retry::strategy::{jitter, ExponentialBackoff};
use tokio_retry::Retry;

use crate::app::AppManager;
use crate::cloud::codec::Codec;
use crate::cloud::mgapp::MGApp;
use crate::cloud::mqtt::{LinkConfig, Linkage, TLSConfig};
use crate::cloud::ota::TaskStateStore;
use crate::cloud::proto::mgcops::{self as proto, Frame, UpgradeStatusAns};
use crate::cloud::root::{self, Root};
use crate::cloud::{mgapp, settings};
use crate::ota::executor::{ops::OTAExecutor, universal::UniversalOTAExecutor};
use crate::ota::resource::HTTPResource;
use crate::{cloud, ota};

const OPS_DEFAULT_TLS_PATH: &str = "/etc/ssl/edsc";
const OPS_DEFAULT_CFG_PATH: &str = "/usr/local/etc/opsd.toml";

struct ManagerInner {
    root: Arc<Root>,
    mg: Arc<Mutex<mgapp::MGApp>>,
    apps: Arc<Mutex<Vec<Box<dyn crate::app::Application + Sync + Send>>>>,
    link: Arc<cloud::MQTT>,
    events: Sender<Event>,
    codec: Arc<Codec>,
    tasks: Arc<Mutex<HashMap<String, (ota::task::TaskHandle, proto::UpgradeStatusAns)>>>,
}

pub(crate) enum AppRefreshKind {
    #[allow(unused)]
    All,
    Specific(Vec<String>),
}
pub(crate) enum Event {
    #[allow(unused)]
    ModelChanged,
    TaskStatusChanged(String, ota::task::Status),
    AppStateChanged(AppRefreshKind),
}
pub struct Manager {
    inner: Arc<ManagerInner>,
}

pub fn serve() {
    tokio::runtime::Builder::new_multi_thread()
        .enable_all()
        .build()
        .unwrap()
        .block_on(async move {
            Manager::serve_cloud().await;
        });
}

#[derive(Deserialize, Serialize)]
pub struct Config {
    app: Option<APMConfig>,
}

impl Manager {
    fn new() -> (Self, Receiver<Event>) {
        let (tx, rx) = tokio::sync::mpsc::channel(16);
        (
            Self {
                inner: Arc::new(ManagerInner::new(&crate::system::sn_get().unwrap(), tx)),
            },
            rx,
        )
    }

    async fn serve_cloud() {
        settings::init_settings();
        let (mgt, rx) = Self::new();
        let inner = mgt.inner.clone();
        mgt.inner.refresh_all_processes().await;
        tokio::spawn(async move {
            inner.poll_events(rx).await;
        });
        let link = mgt.inner.link.clone();
        loop {
            //重连
            if !link.ready() {
                info!("link is not ready");
                // let retry_strategy = FixedInterval::from_millis(10 * 1000).take(3);
                let retry_strategy = ExponentialBackoff::from_millis(1000)
                    .max_delay(Duration::from_secs(60))
                    .map(jitter);
                if Retry::spawn(retry_strategy, || async {
                    info!("connectting...");
                    match mgt.inner.wait_for_ready().await {
                        Ok(_) => {
                            info!("login success");
                            Ok(())
                        }
                        Err(e) => {
                            _ = link.try_disconnect().await;
                            warn!("login failed due to {}", e.to_string());
                            Err(e)
                        }
                    }
                })
                .await
                .is_err()
                {
                    continue;
                } else {
                    //补发升级结果
                    mgt.inner.try_notify_opsd_ok().await;
                }
            }

            match mgt.inner.link.recv_frame().await {
                Ok(frame) => {
                    let inner = mgt.inner.clone();
                    tokio::spawn(async move {
                        match inner.handle_frame(frame).await {
                            Ok(_) => {}
                            Err(e) => {
                                warn!("error = {:?}", e)
                            }
                        }
                    });
                }
                Err(_) => {}
            }
        }
    }
}

#[derive(Deserialize, Serialize)]
struct APMConfig {
    apps: Vec<String>,
}

impl ManagerInner {
    fn new(id: &str, event: Sender<Event>) -> Self {
        let codec = Arc::new(Codec {
            device_id: id.to_string(),
        });
        let envs: HashMap<String, String> = std::env::vars().collect();
        let mut link = cloud::MQTT::new(id, codec.clone());
        let mut cfg = LinkConfig::default();

        //try read config file
        match Self::read_config(
            envs.get("OPS_CFG_PATH")
                .map(|e| e.as_str())
                .unwrap_or(OPS_DEFAULT_CFG_PATH),
        ) {
            Ok(Some(c)) => {
                debug!("load config ok,content = {:?}", c);
                cfg = c;
            }
            Err(e) => {
                warn!("load config failed,error = {}", e.to_string());
            }
            _ => {}
        }

        //overide tls off by environment
        if let Some(val) = envs.get("OPS_TLS_OFF") {
            match val.parse::<bool>() {
                Ok(b) => cfg.tls_off = b,
                Err(_) => {
                }
            }
        }

        //according to "tls off",enable or disable tls
        if cfg.tls_off {
            cfg.tls = None;
        } else if cfg.tls.is_none() {
            cfg.tls = Some(TLSConfig::new_with_path(
                envs.get("OPS_TLS_PATH")
                    .map(|e| e.as_str())
                    .unwrap_or(OPS_DEFAULT_TLS_PATH),
            ));
        }

        link = link.set_config(cfg);
        Self {
            link: Arc::new(link),
            apps: Arc::new(Mutex::new(Self::load_apps().unwrap_or_default())),
            codec,
            events: event,
            tasks: Default::default(),
            root: Arc::new(root::Root::new()),
            mg: Arc::new(Mutex::new(mgapp::MGApp::new())),
        }
    }

    fn read_config(path: &str) -> anyhow::Result<Option<LinkConfig>> {
        if std::fs::metadata(path).is_ok() {
            let str = std::fs::read_to_string(path)?;
            let cfg: LinkConfig = toml::from_str(&str)?;
            Ok(Some(cfg))
        } else {
            Ok(None)
        }
    }

    async fn wait_for_ready(&self) -> anyhow::Result<()> {
        self.link.reconnect().await?;
        self.link.login(Duration::from_secs(10)).await?;
        self.send_model_summary(proto::ModelSummarySendReason::Login)
            .await?;
        Ok(())
    }

    fn load_apps() -> anyhow::Result<Vec<Box<dyn crate::app::Application + Sync + Send>>> {
        let cfg = if std::fs::exists("/var/lib/opsd/apm.toml")? {
            let str = std::fs::read_to_string("/var/lib/opsd/apm.toml")?;
            let cfg: APMConfig = toml::from_str(&str)?;
            cfg
        } else {
            APMConfig {
                apps: vec!["edsc".to_string(), "opsd".to_string(), "embs".to_string()],
            }
        };
        let tool = crate::app::AppToolBuiltin {};
        let mut apps = tool.list().unwrap_or_default();
        apps.retain(|e| cfg.apps.iter().any(|a| a.eq(e.name())));
        Ok(apps)
    }

    #[allow(unused)]
    async fn save_apm_config(&self, cfg: APMConfig) -> anyhow::Result<()> {
        std::fs::create_dir_all("/var/lib/opsd")?;
        std::fs::write("/var/lib/opsd/apm.toml", toml::to_string(&cfg)?)?;
        *self.apps.lock().await = Self::load_apps()?;
        Ok(())
    }

    async fn get_device(&self) -> anyhow::Result<proto::Device> {
        let root = self.root.clone();
        let handle = tokio::task::spawn_blocking(move || root.device());
        let mut root_device = handle.await?;
        let devices = self.mg.lock().await.device().await?;
        root_device.children.extend(devices.into_iter());
        Ok(root_device)
    }

    /// Refresh all processes and send the updated data to the client.
    /// This will refresh both the applications and the microcontroller programs.
    /// It will also send the updated process data to the client.
    /// This is typically called when the model changes or when the application state changes.
    ///
    async fn refresh_all_processes(&self) {
        let updated = {
            let apps = self.apps.lock().await;
            let mut names = vec![];
            for ele in apps.iter() {
                names.push(ele.name());
            }
            let mg = self.mg.lock().await;
            names.extend(mg.programs().await.unwrap_or_default());
            self.root.monitor.lock().unwrap().refresh_specifics(&names)
        };
        _ = self
            .events
            .send(Event::AppStateChanged(AppRefreshKind::Specific(updated)))
            .await;
    }

    async fn send_processes_telemetries(&self, names: &[&str], change: bool) {
        for mut ele in self.root.get_process_rtdata(names).await {
            if change {
                ele.reason = proto::MeasureReason::DataChanged.into();
            }
            _ = self.link.send_frame(self.codec.encode(ele)).await;
        }
    }

    async fn on_task_status_changed(
        &self,
        id: String,
        status: ota::task::Status,
    ) -> anyhow::Result<()> {
        let mut tasks = self.tasks.lock().await;
        if let Some((hd, mut ans)) = tasks.remove(&id) {
            match &status {
                ota::task::Status::Downloading(_) => {
                    ans.upgrade_status = proto::UpgradeStatus::Downloading.into();
                }
                ota::task::Status::Upgrading(_) => {
                    ans.upgrade_status = proto::UpgradeStatus::Upgrading.into();
                }
                ota::task::Status::Finished(r) => {
                    ans.upgrade_status = if r.is_some() {
                        proto::UpgradeStatus::UpgradeFailed.into()
                    } else {
                        proto::UpgradeStatus::UpgradeSuccess.into()
                    };
                }
            }
            self.link.send_frame(self.codec.encode(&ans)).await?;
            TaskStateStore::set_task_status_by_id(&id, ans.upgrade_status());
            if let ota::task::Status::Finished(_) = status {
                _ = self.on_model_changed().await;
            } else {
                tasks.insert(id, (hd, ans));
            }
        }
        Ok(())
    }

    async fn poll_events(&self, mut rx: Receiver<Event>) {
        //monitor master events
        {
            let tx = self.events.clone();
            let micro = self.mg.clone();
            tokio::spawn(async move {
                let mut pid = None;
                loop {
                    pid = MGApp::waiting_master(pid).await;
                    _ = micro.lock().await.reload();
                    _ = tx.send(Event::ModelChanged).await;
                }
            });
        }

        {
            let monitor = self.root.monitor.clone();
            let tx = self.events.clone();
            tokio::spawn(async move {
                loop {
                    if let Ok(changed) = {
                        let cloned = monitor.clone();
                        tokio::task::spawn_blocking(move || {
                            let mut monitor = cloned.lock().unwrap();
                            monitor.refresh_system();
                            monitor.refresh_mmc();
                            monitor
                                .refresh_all_processes()
                                .into_iter()
                                .map(|e| e.to_string())
                                .collect::<Vec<_>>()
                        })
                        .await
                    } {
                        if !changed.is_empty() {
                            _ = tx
                                .send(Event::AppStateChanged(AppRefreshKind::Specific(changed)))
                                .await;
                        }
                    }
                    tokio::time::sleep(std::time::Duration::from_secs(15)).await;
                }
            });
        }
        //handle all events
        loop {
            match rx.recv().await {
                Some(e) => match e {
                    Event::ModelChanged => {
                        _ = self.on_model_changed().await;
                    }
                    Event::TaskStatusChanged(id, status) => {
                        _ = self.on_task_status_changed(id, status).await;
                    }
                    Event::AppStateChanged(kind) => match kind {
                        AppRefreshKind::All => {
                            _ = self.refresh_all_processes().await;
                        }
                        AppRefreshKind::Specific(names) => {
                            _ = self
                                .send_processes_telemetries(
                                    &names.iter().map(|e| e.as_str()).collect::<Vec<_>>(),
                                    true,
                                )
                                .await;
                        }
                    },
                },
                None => {}
            }
        }
    }
}

impl ManagerInner {
    async fn handle_frame(&self, frame: Frame) -> anyhow::Result<()> {
        use proto::MsgType;
        let msg_type: MsgType = frame.msg_type().try_into()?;
        match msg_type {
            MsgType::ModelDetailCall => self.proc_model_call(Codec::decode(&frame)?).await,
            MsgType::AlarmCall => self.proc_alarm_call(Codec::decode(&frame)?).await,
            MsgType::RtDataCall => self.proc_rtdata_call(Codec::decode(&frame)?).await,
            MsgType::UpgradeReq => self.proc_upgrade_request(Codec::decode(&frame)?).await,
            MsgType::UpgradeStatusCall => {
                self.proc_task_query_request(Codec::decode(&frame)?).await
            }

            MsgType::ConfigFileListCall => {
                self.proc_file_list_request(Codec::decode(&frame)?).await
            }

            MsgType::FileSummaryCall => {
                self.proc_file_summary_request(Codec::decode(&frame)?).await
            }

            MsgType::FileProcessingReq => {
                self.proc_file_process_request(Codec::decode(&frame)?).await
            }

            MsgType::RemoteControlReq => self.proc_control_request(Codec::decode(&frame)?).await,
            _ => Ok(()),
        }
    }

    async fn on_model_changed(&self) -> anyhow::Result<()> {
        self.send_model_summary(proto::ModelSummarySendReason::ModelChanged)
            .await?;
        self.refresh_all_processes().await;
        Ok(())
    }

    async fn send_model_summary(
        &self,
        reason: proto::ModelSummarySendReason,
    ) -> anyhow::Result<()> {
        self.link
            .send_frame(self.codec.encode(proto::ModelSummaryReq {
                summary: self.calculate_model_summary().await?,
                reason: reason.try_into().unwrap(),
            }))
            .await?;
        Ok(())
    }

    async fn calculate_model_summary(&self) -> anyhow::Result<String> {
        let device = self.get_device().await?;
        device.encode_to_vec();
        let mut sha = sha2::Sha256::new();
        sha.update(device.encode_to_vec());
        Ok(sha.finalize().encode_hex())
    }

    async fn proc_model_call(&self, _req: proto::ModelDetailCall) -> anyhow::Result<()> {
        let root = self.root.clone();
        let handle = tokio::task::spawn_blocking(move || root.device());
        let mut root_device = handle.await?;
        let devices = self.mg.lock().await.device().await?;
        root_device.children.extend(devices.into_iter());
        for ele in self.apps.lock().await.iter() {
            root_device.children.push(
                if let Ok((version, sha256)) = ele.version() {
                    cloud::Program {
                        name: ele.name().to_string(),
                        version: if version.is_empty() {
                            None
                        } else {
                            Some(version)
                        },
                        sha256: sha256,
                    }
                } else {
                    cloud::Program::new(ele.name())
                }
                .into(),
            );
        }
        self.link
            .send_frame(self.codec.encode(proto::ModelDetailReq {
                root_dev: Some(root_device),
            }))
            .await?;
        Ok(())
    }

    async fn proc_alarm_call(&self, call: proto::AlarmCall) -> anyhow::Result<()> {
        let mut alarms = vec![self.root.get_alarms()?];
        for index in self.mg.lock().await.programs().await? {
            alarms.push(proto::Alarm {
                index: index.to_string(),
                dev_type: "Program".to_string(),
                dev_alarm_list: vec![],
            });
        }

        for app in self.apps.lock().await.iter() {
            alarms.push(proto::Alarm {
                index: app.name().to_string(),
                dev_type: "Program".to_string(),
                dev_alarm_list: vec![],
            });
        }

        self.link
            .send_frame(self.codec.encode(proto::AlarmAns {
                confirm: true,
                alarm_list: alarms,
                request_id: call.request_id,
            }))
            .await?;
        Ok(())
    }

    async fn proc_rtdata_call(&self, call: proto::RtDataCall) -> anyhow::Result<()> {
        self.link
            .send_frame(self.codec.encode(self.root.get_telemetries()))
            .await?;

        if !call.dev_type_list.is_empty() && !call.dev_type_list.iter().any(|e| e.eq("Program")) {
            return Ok(());
        }

        let filter: Box<dyn Fn(&str) -> Option<&str> + Send + Sync> = if call.index_list.is_empty()
        {
            Box::new(|a: &str| Some(a))
        } else {
            Box::new(|e: &str| {
                if call.index_list.iter().any(|f| f.eq(e)) {
                    Some(e)
                } else {
                    None
                }
            })
        };

        _ = self.refresh_all_processes().await;

        for ele in {
            let micro = self.mg.lock().await;
            let names: Vec<&str> = micro
                .programs()
                .await?
                .into_iter()
                .filter(|e| filter(e).is_some())
                .collect();
            self.root.get_process_rtdata(&names).await
        } {
            self.link.send_frame(self.codec.encode(ele)).await?;
        }

        for ele in {
            let apps = self.apps.lock().await;
            let names: Vec<&str> = apps.iter().filter_map(|e| filter(e.name())).collect();
            self.root.get_process_rtdata(&names).await
        } {
            self.link.send_frame(self.codec.encode(ele)).await?;
        }
        Ok(())
    }

    async fn proc_control_request(&self, req: proto::RemoteControlReq) -> anyhow::Result<()> {
        let ret = match req.dev_type.to_lowercase().as_str() {
            "mgc" => self.root.control(&req).await,
            "program" => {
                let cmd = req.cmd.as_ref().ok_or(anyhow!("no cmd"))?;
                let cmd_type = cmd.cmd_type.to_lowercase();
                if req.index.eq("master") {
                    self.mg.lock().await.control(&req).await
                } else if req.index.eq("opsd") {
                    if cmd_type.eq("restart") {
                        _ = crate::ota::executor::ops::reboot_self();
                        Ok(())
                    } else {
                        Err(anyhow::anyhow!("opsd only support Restart"))
                    }
                } else {
                    let apps = self.apps.lock().await;
                    let target = apps.iter().find(|e| e.name().eq(req.index.as_str()));
                    match target {
                        Some(t) => match cmd_type.as_str() {
                            "start" => t.start(),
                            "stop" => t.stop(),
                            "restart" => t.restart(),
                            _ => Err(anyhow::anyhow!("CMD Not Support")),
                        },
                        None => Err(anyhow::anyhow!("Target Not Found")),
                    }
                }
            }
            _ => Err(anyhow::anyhow!("unsupport")),
        };
        self.link
            .send_frame(
                self.codec.encode(proto::RemoteControlAns {
                    send_time: SystemTime::now()
                        .duration_since(UNIX_EPOCH)
                        .unwrap()
                        .as_millis() as i64,
                    dev_type: req.dev_type,
                    index: req.index,
                    result: if ret.is_ok() {
                        proto::Result::Success.into()
                    } else {
                        proto::Result::Failed.into()
                    },
                    request_id: req.request_id,
                    ext: ret.err().map(|e| e.to_string()).unwrap_or_default(),
                }),
            )
            .await?;

        self.refresh_all_processes().await;
        Ok(())
    }

    async fn proc_upgrade_request(&self, req: proto::UpgradeReq) -> anyhow::Result<()> {
        let req_id = req.request_id.clone();
        match self.proc_tasks(req).await {
            Ok(_) => {
                _ = self
                    .link
                    .send_frame(self.codec.encode(proto::UpgradeAns {
                        request_id: req_id,
                        result: proto::Result::Success.into(),
                        failed_reason: "".to_string(),
                    }))
                    .await?;
            }
            Err(e) => {
                _ = self
                    .link
                    .send_frame(self.codec.encode(proto::UpgradeAns {
                        request_id: req_id,
                        result: proto::Result::Failed.into(),
                        failed_reason: e.to_string(),
                    }))
                    .await?;
            }
        }
        Ok(())
    }

    async fn proc_task_query_request(&self, req: proto::UpgradeStatusCall) -> anyhow::Result<()> {
        if let Some(v) = self.get_task_status(&req.task_id).await {
            self.link
                .send_frame(self.codec.encode(proto::UpgradeStatusAns {
                    task_id: req.task_id,
                    task_info: None,
                    upgrade_status: v.into(),
                    fault_info: 0,
                }))
                .await?;
        } else {
            self.link
                .send_frame(self.codec.encode(proto::UpgradeStatusAns {
                    task_id: req.task_id,
                    task_info: None,
                    upgrade_status: proto::UpgradeStatus::TaskLost.into(),
                    fault_info: 0,
                }))
                .await?;
        }
        Ok(())
    }

    async fn proc_file_list_request(&self, req: proto::ConfigFileListCall) -> anyhow::Result<()> {
        _ = self
            .link
            .send_frame(self.codec.encode(proto::ConfigFileListAns {
                config_file_list: settings::list().await?.into_iter().collect(),
                request_id: req.request_id,
            }))
            .await;
        Ok(())
    }

    async fn proc_file_summary_request(&self, req: proto::FileSummaryCall) -> anyhow::Result<()> {
        let content = std::fs::read(&req.config_file).unwrap_or_default();
        let mut hasher = sha2::Sha256::new();
        hasher.update(content);
        let hash = hex::encode(hasher.finalize());
        _ = self
            .link
            .send_frame(self.codec.encode(proto::FileSummaryAns {
                config_file: req.config_file,
                summary: hash,
                request_id: req.request_id,
            }))
            .await;
        Ok(())
    }

    async fn proc_file_process_request(&self, req: proto::FileProcessingReq) -> anyhow::Result<()> {
        use proto::FileProcessingStatus::*;
        let link = self.link.clone();
        let codec = self.codec.clone();
        tokio::spawn(async move {
            let mut ans = proto::FileProcessingAns {
                file: req.file,
                op_type: req.op_type,
                status: ProcessFailed.into(),
                failed_reason: "".to_string(),
                request_id: req.request_id,
            };
            ans.status = Received.into();
            _ = link.send_frame(codec.encode(&ans)).await;
            use proto::FileOpType;
            match ans.op_type() {
                FileOpType::DefaultFileOpType => {}
                FileOpType::Upload => {
                    if let Err(e) = settings::upload(&ans.file, req.download_info.unwrap()).await {
                        ans.status = ProcessFailed.into();
                        ans.failed_reason = e.to_string();
                        _ = link.send_frame(codec.encode(&ans)).await;
                    } else {
                        ans.status = ProcessOk.into();
                        _ = link.send_frame(codec.encode(&ans)).await;
                    }
                }
                FileOpType::Download => {
                    if let Err(e) = settings::download(&ans.file, req.download_info.unwrap()).await
                    {
                        ans.status = ProcessFailed.into();
                        ans.failed_reason = e.to_string();
                        _ = link.send_frame(codec.encode(&ans)).await;
                    } else {
                        ans.status = ProcessOk.into();
                        _ = link.send_frame(codec.encode(&ans)).await;
                    }
                }
            }
        });
        Ok(())
    }
}

impl ManagerInner {
    async fn proc_tasks(&self, req: proto::UpgradeReq) -> anyhow::Result<()> {
        use proto::TaskAct;
        use proto::UpgradeStatus::*;
        let mut tasks = self.tasks.lock().await;
        for x in req.task_list {
            match x.task_act() {
                TaskAct::DefaultTaskAct => {}
                TaskAct::Start => {
                    let index = x
                        .task_info
                        .as_ref()
                        .ok_or(anyhow!("invalid task"))?
                        .dev_info
                        .as_ref()
                        .ok_or(anyhow!("invalid task"))?
                        .index
                        .as_ref();
                    if tasks.iter().any(|e| {
                        e.1 .1
                            .task_info
                            .as_ref()
                            .unwrap()
                            .dev_info
                            .as_ref()
                            .unwrap()
                            .index
                            .eq(&index)
                    }) {
                        return Err(anyhow!("device {} is upgrading", index));
                    }
                    let (task, ans) = self.new_task(x, Some(self.events.clone()))?;
                    let task_id = ans.task_id.clone();
                    tasks.insert(task_id, (task.spawn().await, ans));
                }
                TaskAct::Cancel => {
                    if let Some(task) = tasks.get(&x.task_id) {
                        if task.1.upgrade_status() < Upgrading.into() {
                            task.0.handle.abort();
                            tasks.remove(&x.task_id);
                        } else {
                            warn!(
                                "task {} in status {:?} can not be cancel!",
                                x.task_id,
                                proto::UpgradeStatus::try_from(task.1.upgrade_status()).unwrap()
                            );
                            return Err(anyhow!(format!(
                                "running task {} can not be cancel",
                                x.task_id
                            )));
                        }
                    }
                }
            }
        }
        Ok(())
    }

    fn new_task(
        &self,
        task: proto::TaskType,
        event: Option<Sender<Event>>,
    ) -> anyhow::Result<(ota::task::Task, UpgradeStatusAns)> {
        let task_info = task
            .task_info
            .ok_or(anyhow::anyhow!("task info is empty"))?;
        let dev_obj = task_info
            .dev_info
            .clone()
            .ok_or(anyhow::anyhow!("dev info is empty"))?;
        let download_info = task
            .download_info
            .ok_or(anyhow::anyhow!("download info is empty"))?;
        let log_path = format!("/tmp/ota-{}-latest.log", dev_obj.index);
        let mut log_file = OpenOptions::new()
            .write(true)
            .truncate(true)
            .create(true)
            .open(&log_path)?;
        info!(
            "ota task {} created ok,turn to {} for details",
            task.task_id, log_path
        );
        writeln!(log_file, "task_id   {}", task.task_id)?;
        writeln!(log_file, "resource  {:?}", download_info)?;
        writeln!(log_file, "package   {:?}", task_info)?;
        let ts = TaskStateStore::new(&dev_obj.dev_type, &dev_obj.index);
        ts.set_task_id(&task.task_id);
        Ok((
            crate::ota::task::new_task(
                task.task_id.clone(),
                if dev_obj.index.eq("opsd") {
                    Arc::new(OTAExecutor {})
                } else {
                    Arc::new(UniversalOTAExecutor {})
                },
                Box::new(
                    HTTPResource::new_with_auth(
                        download_info.pkg_url,
                        ota::resource::Authorization::new(
                            download_info.user,
                            download_info.password,
                        ),
                    )
                    .with_sha256(task_info.check_sum.clone()),
                ),
                Box::new(log_file),
                event,
            ),
            proto::UpgradeStatusAns {
                task_id: task.task_id,
                task_info: Some(task_info),
                upgrade_status: 0,
                fault_info: 0,
            },
        ))
    }

    async fn try_notify_opsd_ok(&self) {
        let store = TaskStateStore::new("program".to_string(), "opsd".to_string());
        if let Some(v) = store.task_status() {
            use proto::UpgradeStatus;
            if v != UpgradeStatus::UpgradeSuccess {
                store.set_task_status(UpgradeStatus::UpgradeSuccess);
                let link = self.link.clone();
                let codec = self.codec.clone();
                tokio::spawn(async move {
                    _ = link
                        .send_frame(codec.encode(proto::UpgradeStatusAns {
                            task_id: store.task_id().unwrap_or_default(),
                            task_info: None,
                            upgrade_status: UpgradeStatus::UpgradeSuccess.into(),
                            fault_info: 0,
                        }))
                        .await;
                });
            }
        }
    }

    async fn get_task_status(&self, id: &str) -> Option<proto::UpgradeStatus> {
        let tasks = self.tasks.lock().await;
        if let Some(v) = tasks.get(id) {
            Some(v.1.upgrade_status())
        } else {
            if let Some(v) = TaskStateStore::get_task_status(id) {
                return Some(v);
            } else {
                None
            }
        }
    }
}
