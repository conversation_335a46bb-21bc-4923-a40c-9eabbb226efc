syntax = "proto3";

package mgcops;

option go_package = "proto/mgcops";

// Frame 报文帧格式
message Frame {
  MsgType msg_type = 1; // 消息类型
  int64 send_time = 2; // 发送时间，ms时间戳
  string device_id = 3; // 设备标识
  string protocol_version = 4; // 协议版本
  bytes payload = 5; // 报文正文
  string message_id = 6; // 多包唯一流水号
  int64 packet_num = 7; // 多包总包数
  int64 packet_count = 8; // 多包当前包序号
}

// LoginReq 登录请求
message LoginReq {
  string sn = 1; // 设备sn
  string mac = 2; // 设备mac
  double lat = 3; // 纬度
  double lon = 4; // 经度
  string hardware_version = 5; // 硬件版本
  string software_version = 6; // 软件版本
}

// LoginAns 登录应答
message LoginAns {
  LoginResult result = 1; // 登录结果
}

// ModelSummaryReq 模型摘要信息上送
message ModelSummaryReq {
  string summary = 1; // 模型摘要，md5
  ModelSummarySendReason reason = 2; // 上送原因
}

// ModelSummaryAns 模型摘要信息应答
message ModelSummaryAns {
  string dev_summary = 1; // 设备模型摘要，md5
  string plat_summary = 2; // 平台模型摘要，md5
}

// ModelDetailCall 模型信息召唤
message ModelDetailCall {
  bool skip_props = 1; // 是否忽略静态属性
}

// ModelDetailReq 模型信息上送
message ModelDetailReq {
  Device root_dev = 1; // 设备模型
}

// ModelDetailAns 模型信息应答
message ModelDetailAns {
  bool confirm = 1; // 确认信息
}

// AlarmActiveReq 告警突发上送
message AlarmActiveReq {
  string dev_type = 1; // 设备模型缩写，如：MGC/CCU/PDU
  string index = 2; // 设备类型下索引
  int64 alarm_code = 3; // 告警码
  AlarmReason alarm_reason = 4; // 告警原因
  int64 send_time = 5; // 发送时间，ms时间戳
  string ext = 6; // 协助分析故障信息
}

// AlarmActiveAns 告警突发应答
message AlarmActiveAns {
  bool confirm = 1; // 确认信息
}

// AlarmCall 告警信息召唤
message AlarmCall {
  repeated string dev_type_list = 1; // 设备模型缩写列表
  string request_id = 2; // 消息唯一标识
}

// AlarmAns 告警信息应答
message AlarmAns {
  bool confirm = 1; // 确认信息
  repeated Alarm alarm_list = 2; // 告警信息列表
  string request_id = 3; // 消息唯一标识
}

// RtDataCall 实时数据召唤
message RtDataCall {
  repeated string dev_type_list = 1; // 需要召唤的设备类型,不填则召唤所有设备
  repeated string index_list = 2; // 在DevTypeList的基础上指定需要召唤的设备列表,不填就是召唤所有设备类型下的涉笔
  repeated string measure_index_list = 3; // 需要召唤的索引数据,不填就是召唤所有的数据
}

// RtDataAns 实时数据应答
message RtDataAns {
  string dev_type = 1; // 设备类型
  string index = 2; // 设备索引
  repeated Telemetry telemetry = 3; // 遥测
  repeated StateOrSignal state_or_signal = 4; // 状态或遥信
  repeated StringData string_data = 5; // 文本数据
  MeasureReason reason = 6; // 传输原因
}

// UpgradeReq 升级任务下发请求
message UpgradeReq {
  int64 task_num = 1; // 升级任务个数
  int64 send_time = 2; // 发送时间，ms时间戳
  repeated TaskType task_list = 3; // 升级任务列表
  string request_id = 4; // 消息唯一标识
}

// UpgradeAns 升级任务下发应答
message UpgradeAns {
  string request_id = 1; // 消息唯一标识
  Result result = 2; // 结果
  string failed_reason = 3; // 失败原因
}

// UpgradeStatusCall 升级状态召唤
message UpgradeStatusCall {
  string task_id = 1; // 任务标识
  TaskDescType task_info = 2; // 任务描述
}

// UpgradeStatusAns 升级状态应答
message UpgradeStatusAns {
  string task_id = 1; // 任务标识
  TaskDescType task_info = 2; // 任务描述
  UpgradeStatus upgrade_status = 3; // 升级状态
  int64 fault_info = 4; // 错误信息
}

// ConfigFileListCall 配置文件列表召唤
message ConfigFileListCall {
  string request_id = 1; // 消息唯一标识
}

// ConfigFileListAns 配置文件列表应答
message ConfigFileListAns {
  map<string, string> config_file_list = 1; // key:配置文件简称, value:配置文件的绝对路径
  string request_id = 2; // 消息唯一标识
}

// FileSummaryCall 文件摘要召唤
message FileSummaryCall {
  string config_file = 1; // 配置文件的绝对路径
  string request_id = 2; // 消息唯一标识
}

// FileSummaryAns 文件摘要应答
message FileSummaryAns {
  string config_file = 1; // 配置文件的绝对路径
  string summary = 2; // 文件摘要，sha256
  string request_id = 3; // 消息唯一标识
}

// FileProcessingReq 文件操作请求
message FileProcessingReq {
  string file = 1; // 配置文件的绝对路径
  FileOpType op_type = 2; // 文件操作类型
  DownloadInfo download_info = 3; // 下载信息
  string request_id = 4; // 消息唯一标识
}

// FileProcessingAns 文件操作应答
message FileProcessingAns {
  string file = 1; // 配置文件的绝对路径
  FileOpType op_type = 2; // 文件操作类型
  FileProcessingStatus status = 3; // 文件操作状态
  string failed_reason = 4; // 失败原因
  string request_id = 5; // 消息唯一标识
}

// RemoteControlReq 远程控制请求
message RemoteControlReq {
  int64 send_time = 1; // 发送时间，ms时间戳
  string dev_type = 2; // 设备模型缩写，如：MGC/CCU/PDU
  string index = 3; // 设备类型下索引
  CmdInfo cmd = 4; // 控制指令信息
  string request_id = 5; // 消息唯一标识
}

// RemoteControlAns 远程控制应答
message RemoteControlAns {
  int64 send_time = 1; // 发送时间，ms时间戳
  string dev_type = 2; // 设备模型缩写，如：MGC/CCU/PDU
  string index = 3; // 设备类型下索引
  Result result = 4; // 结果
  string request_id = 5; // 消息唯一标识
  string ext = 6; // 携带自定义的错误信息
}

// ==== 类类型 ====

// Device 设备模型
message Device {
  string dev_type = 1; // 设备模型缩写，如：MGC/CCU/PDU
  bool virtual = 2; // 是否虚拟设备
  string sn = 3; // 设备sn
  string index = 4; // 设备类型下索引
  string version = 5; // 软件版本号
  map<string, string> props = 6; // 设备属性
  repeated Device children = 7; // 子设备
  string vendor = 8; // 设备厂商
  string model = 9; // 设备型号
}

// DevAlarm 设备告警信息
message DevAlarm {
  int64 alarm_code = 1; // 告警码
  int64 alarm_time = 2; // 告警时间，ms时间戳
  string ext = 3; // 携带自定义的错误信息
}

// Alarm 告警信息
message Alarm {
  string index = 1; // 设备类型下索引
  string dev_type = 2; // 设备类型
  repeated DevAlarm dev_alarm_list = 3; // 设备告警
}

// TimeSpanType 升级时间段类型定义
message TimeSpanType {
  string begin = 1; // 开始时间，格式：20:00
  string end = 2; // 结束时间，格式：04:00
}

// DevObjType 设备对象定义
message DevObjType {
  string dev_type = 1; // 设备模型缩写，如：MGC/CCU/PDU
  string index = 2; // 设备类型下索引
  string version = 3; // 软件版本号
}

// TaskDescType 任务描述定义
message TaskDescType {
  DevObjType dev_info = 1; // 设备信息
  string pkg_name = 2; // 升级包文件名称
  string pkg_version = 3; // 升级包版本
  string check_sum = 4; // 升级包校验码，sha256
}

// DownloadInfo 下载对象定义
message DownloadInfo {
  DownloadType type = 1; // 下载方式枚举
  string pkg_url = 2; // 完整的下载地址路径，包含包名称
  string user = 3; // 用户名
  string password = 4; // 密码
}

// TaskType 任务对象
message TaskType {
  string task_id = 1; // 任务标识
  string bind_task_id = 2; // 绑定任务标识
  UpgradeType upgrade_type = 3; // 升级方式枚举
  TaskAct task_act = 4; // 任务动作枚举
  bool if_reboot = 5; // 是否重启
  repeated TimeSpanType time_span_list = 6; // 执行空闲升级的时间段
  TaskDescType task_info = 7; // 任务描述
  DownloadInfo download_info = 8; // 升级包下载信息
}

// CmdInfo 控制指令信息
message CmdInfo {
  string cmd_type = 1; // 控制指令类型，参见指令类型枚举
  repeated string args = 2; // 控制指令参数
}

// Telemetry 遥测数据
message Telemetry {
  string addr = 1;
  double value = 2;
}

// StateOrSignal 状态或者遥信
message StateOrSignal {
  string addr = 1;
  int64 value = 2;
}

// StringData 字符数据
message StringData {
  string addr = 1;
  string value = 2;
}

// ==== 枚举类型 ====

// DownloadType 下载方式枚举
enum DownloadType {
  DEFAULT_DOWNLOAD_TYPE = 0; // 默认下载方式
  FTP = 1; // FTP下载
  HTTPS = 2; // HTTPS下载
  FTPS = 3; // FTPS下载
}

// UpgradeType 升级方式枚举
enum UpgradeType {
  DEFAULT_UPGRADE_TYPE = 0; // 默认升级方式
  AUTO = 1; // 自动空闲升级
  SEMI_AUTO = 2; // 半自动升级
  MANUAL = 3; // 手动升级
  NEW_CTRL = 4; // 新控制器拉取程序包
  EMERGENCY = 5; // 紧急强制立即升级
}

// TaskAct 任务动作枚举
enum TaskAct {
  DEFAULT_TASK_ACT = 0; // 默认任务动作
  START = 1; // 开始
  CANCEL = 2; // 取消
}

// LoginResult 登录结果枚举
enum LoginResult {
  DEFAULT_LOGIN_RESULT = 0; // 默认登录结果
  LOGIN_SUCCESS = 1; // 成功
  LOGIN_FAILED_DUP_LOGIN = 2; // 失败-重复上线
  LOGIN_FAILED_INVALID_DEV = 3; // 失败-非法设备
  LOGIN_FAILED_UNKNOWN = 255; // 失败-未知原因
}

// ModelSummarySendReason 模型摘要信息上送原因
enum ModelSummarySendReason {
  DEFAULT_MODEL_SUMMARY_SEND_REASON = 0; // 默认模型摘要信息上送原因
  LOGIN = 1; // 上线初次发送
  MODEL_CHANGED = 2; // 模型发生变更
  PERIOD = 3; // 周期上送
}

// AlarmReason 告警原因枚举
enum AlarmReason {
  DEFAULT_ALARM_REASON = 0; // 默认告警原因
  ACTIVE = 1; // 发送
  RESUME = 2; // 恢复
}

// UpgradeStatus 升级状态枚举
enum UpgradeStatus {
  INIT_UPGRADE = 0; // 初始化升级
  WAITING_DOWNLOAD = 1; // 等待下载
  DOWNLOADING = 2; // 下载中
  READY_UPGRADE = 3; // 准备升级
  WAITING_DEV_IDLE = 4; // 等待设备空闲
  UPGRADING = 5; // 升级中
  WAITING_DEV_REBOOT = 6; // 等待设备重启
  WAITING_UPGRADE_RESULT = 7; // 等待升级结果
  UPGRADE_SUCCESS = 8; // 升级成功
  UPGRADE_FAILED = 9; // 升级失败
  TASK_LOST = 99; // 任务丢失
}

// FileOpType 文件操作类型枚举
enum FileOpType {
  DEFAULT_FILE_OP_TYPE = 0; // 默认文件操作类型
  UPLOAD = 1; // 上传
  DOWNLOAD = 2; // 下载
}

// FileProcessingStatus 文件操作状态枚举
enum FileProcessingStatus {
  DEFAULT_FILE_PROCESSING_STATUS = 0; // 默认文件操作状态
  RECEIVED = 1; // 已收到任务
  PROCESSING = 2; // 执行中
  PROCESS_OK = 3; // 执行成功
  PROCESS_FAILED = 4; // 执行失败
}

// Result 结果枚举
enum Result {
  DEFAULT_RESULT = 0; // 默认结果
  SUCCESS = 1; // 成功
  FAILED = 2; // 失败
}

// MsgType 消息类型枚举
enum MsgType {
  DEFAULT_MSG_TYPE = 0; // 默认消息类型

  LOGIN_REQ = 1; // 登录请求
  LOGIN_ANS = 2; // 登录应答

  MODEL_SUMMARY_REQ = 3; // 模型摘要信息请求
  MODEL_SUMMARY_ANS = 4; // 模型摘要信息应答
  MODEL_DETAIL_CALL = 5; // 模型信息召唤
  MODEL_DETAIL_REQ = 6; // 模型信息上送
  MODEL_DETAIL_ANS = 7; // 模型信息应答

  ALARM_ACTIVE_REQ = 8; // 告警突发上送
  ALARM_ACTIVE_ANS = 9; // 告警突发应答
  ALARM_CALL = 10; // 告警信息召唤
  ALARM_ANS = 11; // 告警信息应答

  RT_DATA_CALL = 12; // 实时数据召唤
  RT_DATA_ANS = 13; // 实时数据应答

  UPGRADE_REQ = 14; // 升级任务下发请求
  UPGRADE_STATUS_CALL = 15; // 升级状态召唤
  UPGRADE_STATUS_ANS = 16; // 升级状态应答

  CONFIG_FILE_LIST_CALL = 17; // 配置文件列表召唤
  CONFIG_FILE_LIST_ANS = 18; // 配置文件列表应答

  FILE_SUMMARY_CALL = 19; // 文件摘要召唤
  FILE_SUMMARY_ANS = 20; // 文件摘要应答
  FILE_PROCESSING_REQ = 21; // 文件操作请求
  FILE_PROCESSING_ANS = 22; // 文件操作应答

  REMOTE_CONTROL_REQ = 23; // 远程控制请求
  REMOTE_CONTROL_ANS = 24; // 远程控制应答

  UPGRADE_ANS = 25; // 升级任务下发应答
}

// MeasureReason 量测传输原因
enum MeasureReason {
  DEFAULT_MEASURE_REASON = 0; // 默认
  CALL_ANS = 1; // 响应召唤
  DATA_CHANGED = 2; // 变化上送
}
