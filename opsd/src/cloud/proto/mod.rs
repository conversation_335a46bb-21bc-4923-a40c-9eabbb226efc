/*
 * Copyright (c) 2024. DERI. Nanjing, China. All rights reserved
 * FILENAME: mod.rs
 * PROJECT: opsd
 * AUTHOR: shenshuangquan
 */

use log::warn;
use num_enum::{IntoPrimitive, TryFromPrimitive};

use mgcops::{
    AlarmActiveAns, AlarmActiveReq, AlarmAns, AlarmCall, ConfigFileListAns, ConfigFileListCall,
    FileProcessingAns, FileProcessingReq, FileSummaryAns, FileSummaryCall, LoginAns, LoginReq,
    ModelDetailAns, ModelDetailCall, ModelDetailReq, ModelSummaryAns, ModelSummaryReq, MsgType,
    RemoteControlAns, RemoteControlReq, RtDataAns, RtDataCall, StateOrSignal, StringData,
    UpgradeAns, UpgradeReq, UpgradeStatusAns, UpgradeStatusCall,
};
use Topic::{Alarm, Control, Login, Measure, Model, Setting, Unknown, Upgrade};

use crate::{cloud::proto::mgcops::Telemetry};
pub mod mgcops;

impl Into<RtDataAns> for &[(&str, serde_json::Value)] {
    fn into(self) -> RtDataAns {
        let mut r = RtDataAns::default();
        for ele in self {
            match &ele.1 {
                serde_json::Value::Null => {
                    warn!("invalid null value: {:?}", ele.0);
                }
                serde_json::Value::Bool(v) => {
                    r.state_or_signal.push(StateOrSignal {
                        addr: ele.0.to_string(),
                        value: *v as i64,
                    });
                }
                serde_json::Value::Number(v) => {
                    if v.is_i64() {
                        r.state_or_signal.push(StateOrSignal {
                            addr: ele.0.to_string(),
                            value: v.as_i64().unwrap(),
                        });
                    } else if v.is_f64() {
                        r.telemetry.push(Telemetry {
                            addr: ele.0.to_string(),
                            value: v.as_f64().unwrap(),
                        });
                    } else {
                        warn!("invalid number: {:?}", v);
                    }
                }
                serde_json::Value::String(v) => r.string_data.push(StringData {
                    addr: ele.0.to_string(),
                    value: v.clone(),
                }),
                _ => {
                    warn!("invalid value: {:?}", ele.1);
                }
            }
        }
        r
    }
}

pub const TOPICS: [&str; 7] = [
    "Login", "Model", "Alarm", "Measure", "Upgrade", "Settings", "Control",
];

pub const DEFAULT_VENDOR: &str = "teld";

pub trait MessageType {
    fn name(&self) -> &'static str;
    fn group(&self) -> Topic;
    fn msg_type(&self) -> MsgType;
}

#[derive(Debug, Hash, Eq, PartialEq)]
pub enum Topic {
    Unknown = -1,
    Login = 0,
    Model = 1,
    Alarm = 2,
    Measure = 3,
    Upgrade = 4,
    Setting = 5,
    Control = 6,
}

impl From<&str> for Topic {
    fn from(value: &str) -> Self {
        if let Some(pos) = TOPICS.iter().position(|&e| e.eq(value)) {
            pos.into()
        } else {
            Unknown
        }
    }
}

impl Into<&'static str> for Topic {
    fn into(self) -> &'static str {
        let idx: usize = self as usize;
        TOPICS[idx]
    }
}

impl From<usize> for Topic {
    fn from(value: usize) -> Self {
        match value {
            0 => Login,
            1 => Model,
            2 => Alarm,
            3 => Measure,
            4 => Upgrade,
            5 => Setting,
            6 => Control,
            _ => Unknown,
        }
    }
}

impl MessageType for MsgType {
    fn name(&self) -> &'static str {
        self.as_str_name()
    }

    fn group(&self) -> Topic {
        use MsgType::*;
        match self {
            DefaultMsgType => Unknown,
            LoginReq | LoginAns => Topic::Login,
            ModelSummaryReq | ModelSummaryAns | ModelDetailCall | ModelDetailReq
            | ModelDetailAns => Topic::Model,
            AlarmActiveReq | AlarmActiveAns | AlarmCall | AlarmAns => Topic::Alarm,
            RtDataCall | RtDataAns => Topic::Measure,
            UpgradeReq | UpgradeStatusAns | MsgType::UpgradeStatusCall | UpgradeAns => {
                Topic::Upgrade
            }
            ConfigFileListCall | ConfigFileListAns | FileSummaryAns | FileSummaryCall
            | FileProcessingReq | FileProcessingAns => Topic::Setting,
            RemoteControlReq | RemoteControlAns => Topic::Control,
        }
    }

    fn msg_type(&self) -> MsgType {
        *self
    }
}

macro_rules! bind_struct_with_message_type {
        ($($struct_name:path => $msg_type:path),*) => {
        $(
            impl MessageType for $struct_name {
                fn name(&self)->&'static str {
                    $msg_type.name()
                }
                fn group(&self)-> Topic {
                    $msg_type.group()
                }
                fn msg_type(&self) -> MsgType {
                    $msg_type
                }
            }

            impl AsRef<$struct_name> for $struct_name {
                fn as_ref(&self) -> &$struct_name {
                    self
                }
            }
        )*
    };
}

// macro_rules! requests {
//     ($($struct_name:path),*) => {
//         $(impl Request for $struct_name {
//             fn request_id(&self) -> &str {
//                 &self.request_id
//             }
//         }
//         )*
//     };
// }

bind_struct_with_message_type!(
    LoginAns => MsgType::LoginAns,
    ModelSummaryAns => MsgType::ModelSummaryAns,
    ModelDetailAns => MsgType::ModelDetailAns,
    AlarmCall => MsgType::AlarmCall,
    AlarmActiveAns => MsgType::AlarmActiveAns,
    ModelDetailCall => MsgType::ModelDetailCall,
    UpgradeReq => MsgType::UpgradeReq,
    UpgradeAns => MsgType::UpgradeAns,
    UpgradeStatusCall => MsgType::UpgradeStatusCall,
    ConfigFileListCall => MsgType::ConfigFileListCall,
    FileSummaryCall => MsgType::FileSummaryCall,
    FileProcessingReq => MsgType::FileProcessingReq,
    LoginReq => MsgType::LoginReq,
    ModelSummaryReq => MsgType::ModelSummaryReq,
    ModelDetailReq => MsgType::ModelDetailReq,
    AlarmActiveReq => MsgType::AlarmActiveReq,
    AlarmAns => MsgType::AlarmAns,
    RtDataAns => MsgType::RtDataAns,
    UpgradeStatusAns => MsgType::UpgradeStatusAns,
    FileSummaryAns => MsgType::FileSummaryAns,
    ConfigFileListAns => MsgType::ConfigFileListAns,
    FileProcessingAns => MsgType::FileProcessingAns,
    RemoteControlAns => MsgType::RemoteControlAns,
    RemoteControlReq => MsgType::RemoteControlReq,
    RtDataCall => MsgType::RtDataCall
);

// fn recur(
//     ves: &HashMap<ModelIndex, Vec<ModelIndex>>,
//     dev: &mut Device,
//     i2m: &HashMap<ModelIndex, Metadata>,
// ) {
//     let model_id = ModelIndex::new(dev.dev_type.clone(), dev.index.clone());
//     if let Some(subs) = ves.get(&model_id) {
//         for x in subs {
//             let meta = i2m.get(x).unwrap();
//             let mut sub = Device::from(meta.clone());
//             recur(ves, &mut sub, i2m);
//             dev.children.push(sub);
//         }
//     }
// }

#[derive(IntoPrimitive, TryFromPrimitive)]
#[repr(u8)]
pub enum ProcessStatus {
    Running = 1,
    Stopped = 2,
    Abnormal = 3,
}

#[derive(IntoPrimitive, TryFromPrimitive)]
#[repr(u32)]
pub enum NetApprochMethod {
    Celluar = 1,
    Router = 2,
    Unknown = 3,
    Wifi = 4,
}
