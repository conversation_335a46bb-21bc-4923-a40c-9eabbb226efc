// This file is @generated by prost-build.
/// Frame 报文帧格式
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct Frame {
    /// 消息类型
    #[prost(enumeration = "MsgType", tag = "1")]
    pub msg_type: i32,
    /// 发送时间，ms时间戳
    #[prost(int64, tag = "2")]
    pub send_time: i64,
    /// 设备标识
    #[prost(string, tag = "3")]
    pub device_id: ::prost::alloc::string::String,
    /// 协议版本
    #[prost(string, tag = "4")]
    pub protocol_version: ::prost::alloc::string::String,
    /// 报文正文
    #[prost(bytes = "vec", tag = "5")]
    pub payload: ::prost::alloc::vec::Vec<u8>,
    /// 多包唯一流水号
    #[prost(string, tag = "6")]
    pub message_id: ::prost::alloc::string::String,
    /// 多包总包数
    #[prost(int64, tag = "7")]
    pub packet_num: i64,
    /// 多包当前包序号
    #[prost(int64, tag = "8")]
    pub packet_count: i64,
}
/// LoginReq 登录请求
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct LoginReq {
    /// 设备sn
    #[prost(string, tag = "1")]
    pub sn: ::prost::alloc::string::String,
    /// 设备mac
    #[prost(string, tag = "2")]
    pub mac: ::prost::alloc::string::String,
    /// 纬度
    #[prost(double, tag = "3")]
    pub lat: f64,
    /// 经度
    #[prost(double, tag = "4")]
    pub lon: f64,
    /// 硬件版本
    #[prost(string, tag = "5")]
    pub hardware_version: ::prost::alloc::string::String,
    /// 软件版本
    #[prost(string, tag = "6")]
    pub software_version: ::prost::alloc::string::String,
}
/// LoginAns 登录应答
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, Copy, PartialEq, ::prost::Message)]
pub struct LoginAns {
    /// 登录结果
    #[prost(enumeration = "LoginResult", tag = "1")]
    pub result: i32,
}
/// ModelSummaryReq 模型摘要信息上送
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct ModelSummaryReq {
    /// 模型摘要，md5
    #[prost(string, tag = "1")]
    pub summary: ::prost::alloc::string::String,
    /// 上送原因
    #[prost(enumeration = "ModelSummarySendReason", tag = "2")]
    pub reason: i32,
}
/// ModelSummaryAns 模型摘要信息应答
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct ModelSummaryAns {
    /// 设备模型摘要，md5
    #[prost(string, tag = "1")]
    pub dev_summary: ::prost::alloc::string::String,
    /// 平台模型摘要，md5
    #[prost(string, tag = "2")]
    pub plat_summary: ::prost::alloc::string::String,
}
/// ModelDetailCall 模型信息召唤
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, Copy, PartialEq, ::prost::Message)]
pub struct ModelDetailCall {
    /// 是否忽略静态属性
    #[prost(bool, tag = "1")]
    pub skip_props: bool,
}
/// ModelDetailReq 模型信息上送
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct ModelDetailReq {
    /// 设备模型
    #[prost(message, optional, tag = "1")]
    pub root_dev: ::core::option::Option<Device>,
}
/// ModelDetailAns 模型信息应答
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, Copy, PartialEq, ::prost::Message)]
pub struct ModelDetailAns {
    /// 确认信息
    #[prost(bool, tag = "1")]
    pub confirm: bool,
}
/// AlarmActiveReq 告警突发上送
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct AlarmActiveReq {
    /// 设备模型缩写，如：MGC/CCU/PDU
    #[prost(string, tag = "1")]
    pub dev_type: ::prost::alloc::string::String,
    /// 设备类型下索引
    #[prost(string, tag = "2")]
    pub index: ::prost::alloc::string::String,
    /// 告警码
    #[prost(int64, tag = "3")]
    pub alarm_code: i64,
    /// 告警原因
    #[prost(enumeration = "AlarmReason", tag = "4")]
    pub alarm_reason: i32,
    /// 发送时间，ms时间戳
    #[prost(int64, tag = "5")]
    pub send_time: i64,
    /// 协助分析故障信息
    #[prost(string, tag = "6")]
    pub ext: ::prost::alloc::string::String,
}
/// AlarmActiveAns 告警突发应答
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, Copy, PartialEq, ::prost::Message)]
pub struct AlarmActiveAns {
    /// 确认信息
    #[prost(bool, tag = "1")]
    pub confirm: bool,
}
/// AlarmCall 告警信息召唤
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct AlarmCall {
    /// 设备模型缩写列表
    #[prost(string, repeated, tag = "1")]
    pub dev_type_list: ::prost::alloc::vec::Vec<::prost::alloc::string::String>,
    /// 消息唯一标识
    #[prost(string, tag = "2")]
    pub request_id: ::prost::alloc::string::String,
}
/// AlarmAns 告警信息应答
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct AlarmAns {
    /// 确认信息
    #[prost(bool, tag = "1")]
    pub confirm: bool,
    /// 告警信息列表
    #[prost(message, repeated, tag = "2")]
    pub alarm_list: ::prost::alloc::vec::Vec<Alarm>,
    /// 消息唯一标识
    #[prost(string, tag = "3")]
    pub request_id: ::prost::alloc::string::String,
}
/// RtDataCall 实时数据召唤
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct RtDataCall {
    /// 需要召唤的设备类型,不填则召唤所有设备
    #[prost(string, repeated, tag = "1")]
    pub dev_type_list: ::prost::alloc::vec::Vec<::prost::alloc::string::String>,
    /// 在DevTypeList的基础上指定需要召唤的设备列表,不填就是召唤所有设备类型下的涉笔
    #[prost(string, repeated, tag = "2")]
    pub index_list: ::prost::alloc::vec::Vec<::prost::alloc::string::String>,
    /// 需要召唤的索引数据,不填就是召唤所有的数据
    #[prost(string, repeated, tag = "3")]
    pub measure_index_list: ::prost::alloc::vec::Vec<::prost::alloc::string::String>,
}
/// RtDataAns 实时数据应答
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct RtDataAns {
    /// 设备类型
    #[prost(string, tag = "1")]
    pub dev_type: ::prost::alloc::string::String,
    /// 设备索引
    #[prost(string, tag = "2")]
    pub index: ::prost::alloc::string::String,
    /// 遥测
    #[prost(message, repeated, tag = "3")]
    pub telemetry: ::prost::alloc::vec::Vec<Telemetry>,
    /// 状态或遥信
    #[prost(message, repeated, tag = "4")]
    pub state_or_signal: ::prost::alloc::vec::Vec<StateOrSignal>,
    /// 文本数据
    #[prost(message, repeated, tag = "5")]
    pub string_data: ::prost::alloc::vec::Vec<StringData>,
    /// 传输原因
    #[prost(enumeration = "MeasureReason", tag = "6")]
    pub reason: i32,
}
/// UpgradeReq 升级任务下发请求
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct UpgradeReq {
    /// 升级任务个数
    #[prost(int64, tag = "1")]
    pub task_num: i64,
    /// 发送时间，ms时间戳
    #[prost(int64, tag = "2")]
    pub send_time: i64,
    /// 升级任务列表
    #[prost(message, repeated, tag = "3")]
    pub task_list: ::prost::alloc::vec::Vec<TaskType>,
    /// 消息唯一标识
    #[prost(string, tag = "4")]
    pub request_id: ::prost::alloc::string::String,
}
/// UpgradeAns 升级任务下发应答
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct UpgradeAns {
    /// 消息唯一标识
    #[prost(string, tag = "1")]
    pub request_id: ::prost::alloc::string::String,
    /// 结果
    #[prost(enumeration = "Result", tag = "2")]
    pub result: i32,
    /// 失败原因
    #[prost(string, tag = "3")]
    pub failed_reason: ::prost::alloc::string::String,
}
/// UpgradeStatusCall 升级状态召唤
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct UpgradeStatusCall {
    /// 任务标识
    #[prost(string, tag = "1")]
    pub task_id: ::prost::alloc::string::String,
    /// 任务描述
    #[prost(message, optional, tag = "2")]
    pub task_info: ::core::option::Option<TaskDescType>,
}
/// UpgradeStatusAns 升级状态应答
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct UpgradeStatusAns {
    /// 任务标识
    #[prost(string, tag = "1")]
    pub task_id: ::prost::alloc::string::String,
    /// 任务描述
    #[prost(message, optional, tag = "2")]
    pub task_info: ::core::option::Option<TaskDescType>,
    /// 升级状态
    #[prost(enumeration = "UpgradeStatus", tag = "3")]
    pub upgrade_status: i32,
    /// 错误信息
    #[prost(int64, tag = "4")]
    pub fault_info: i64,
}
/// ConfigFileListCall 配置文件列表召唤
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct ConfigFileListCall {
    /// 消息唯一标识
    #[prost(string, tag = "1")]
    pub request_id: ::prost::alloc::string::String,
}
/// ConfigFileListAns 配置文件列表应答
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct ConfigFileListAns {
    /// key:配置文件简称, value:配置文件的绝对路径
    #[prost(map = "string, string", tag = "1")]
    pub config_file_list:
        ::std::collections::HashMap<::prost::alloc::string::String, ::prost::alloc::string::String>,
    /// 消息唯一标识
    #[prost(string, tag = "2")]
    pub request_id: ::prost::alloc::string::String,
}
/// FileSummaryCall 文件摘要召唤
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct FileSummaryCall {
    /// 配置文件的绝对路径
    #[prost(string, tag = "1")]
    pub config_file: ::prost::alloc::string::String,
    /// 消息唯一标识
    #[prost(string, tag = "2")]
    pub request_id: ::prost::alloc::string::String,
}
/// FileSummaryAns 文件摘要应答
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct FileSummaryAns {
    /// 配置文件的绝对路径
    #[prost(string, tag = "1")]
    pub config_file: ::prost::alloc::string::String,
    /// 文件摘要，sha256
    #[prost(string, tag = "2")]
    pub summary: ::prost::alloc::string::String,
    /// 消息唯一标识
    #[prost(string, tag = "3")]
    pub request_id: ::prost::alloc::string::String,
}
/// FileProcessingReq 文件操作请求
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct FileProcessingReq {
    /// 配置文件的绝对路径
    #[prost(string, tag = "1")]
    pub file: ::prost::alloc::string::String,
    /// 文件操作类型
    #[prost(enumeration = "FileOpType", tag = "2")]
    pub op_type: i32,
    /// 下载信息
    #[prost(message, optional, tag = "3")]
    pub download_info: ::core::option::Option<DownloadInfo>,
    /// 消息唯一标识
    #[prost(string, tag = "4")]
    pub request_id: ::prost::alloc::string::String,
}
/// FileProcessingAns 文件操作应答
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct FileProcessingAns {
    /// 配置文件的绝对路径
    #[prost(string, tag = "1")]
    pub file: ::prost::alloc::string::String,
    /// 文件操作类型
    #[prost(enumeration = "FileOpType", tag = "2")]
    pub op_type: i32,
    /// 文件操作状态
    #[prost(enumeration = "FileProcessingStatus", tag = "3")]
    pub status: i32,
    /// 失败原因
    #[prost(string, tag = "4")]
    pub failed_reason: ::prost::alloc::string::String,
    /// 消息唯一标识
    #[prost(string, tag = "5")]
    pub request_id: ::prost::alloc::string::String,
}
/// RemoteControlReq 远程控制请求
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct RemoteControlReq {
    /// 发送时间，ms时间戳
    #[prost(int64, tag = "1")]
    pub send_time: i64,
    /// 设备模型缩写，如：MGC/CCU/PDU
    #[prost(string, tag = "2")]
    pub dev_type: ::prost::alloc::string::String,
    /// 设备类型下索引
    #[prost(string, tag = "3")]
    pub index: ::prost::alloc::string::String,
    /// 控制指令信息
    #[prost(message, optional, tag = "4")]
    pub cmd: ::core::option::Option<CmdInfo>,
    /// 消息唯一标识
    #[prost(string, tag = "5")]
    pub request_id: ::prost::alloc::string::String,
}
/// RemoteControlAns 远程控制应答
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct RemoteControlAns {
    /// 发送时间，ms时间戳
    #[prost(int64, tag = "1")]
    pub send_time: i64,
    /// 设备模型缩写，如：MGC/CCU/PDU
    #[prost(string, tag = "2")]
    pub dev_type: ::prost::alloc::string::String,
    /// 设备类型下索引
    #[prost(string, tag = "3")]
    pub index: ::prost::alloc::string::String,
    /// 结果
    #[prost(enumeration = "Result", tag = "4")]
    pub result: i32,
    /// 消息唯一标识
    #[prost(string, tag = "5")]
    pub request_id: ::prost::alloc::string::String,
    /// 携带自定义的错误信息
    #[prost(string, tag = "6")]
    pub ext: ::prost::alloc::string::String,
}
/// Device 设备模型
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct Device {
    /// 设备模型缩写，如：MGC/CCU/PDU
    #[prost(string, tag = "1")]
    pub dev_type: ::prost::alloc::string::String,
    /// 是否虚拟设备
    #[prost(bool, tag = "2")]
    pub r#virtual: bool,
    /// 设备sn
    #[prost(string, tag = "3")]
    pub sn: ::prost::alloc::string::String,
    /// 设备类型下索引
    #[prost(string, tag = "4")]
    pub index: ::prost::alloc::string::String,
    /// 软件版本号
    #[prost(string, tag = "5")]
    pub version: ::prost::alloc::string::String,
    /// 设备属性
    #[prost(map = "string, string", tag = "6")]
    pub props:
        ::std::collections::HashMap<::prost::alloc::string::String, ::prost::alloc::string::String>,
    /// 子设备
    #[prost(message, repeated, tag = "7")]
    pub children: ::prost::alloc::vec::Vec<Device>,
    /// 设备厂商
    #[prost(string, tag = "8")]
    pub vendor: ::prost::alloc::string::String,
    /// 设备型号
    #[prost(string, tag = "9")]
    pub model: ::prost::alloc::string::String,
}
/// DevAlarm 设备告警信息
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct DevAlarm {
    /// 告警码
    #[prost(int64, tag = "1")]
    pub alarm_code: i64,
    /// 告警时间，ms时间戳
    #[prost(int64, tag = "2")]
    pub alarm_time: i64,
    /// 携带自定义的错误信息
    #[prost(string, tag = "3")]
    pub ext: ::prost::alloc::string::String,
}
/// Alarm 告警信息
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct Alarm {
    /// 设备类型下索引
    #[prost(string, tag = "1")]
    pub index: ::prost::alloc::string::String,
    /// 设备类型
    #[prost(string, tag = "2")]
    pub dev_type: ::prost::alloc::string::String,
    /// 设备告警
    #[prost(message, repeated, tag = "3")]
    pub dev_alarm_list: ::prost::alloc::vec::Vec<DevAlarm>,
}
/// TimeSpanType 升级时间段类型定义
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct TimeSpanType {
    /// 开始时间，格式：20:00
    #[prost(string, tag = "1")]
    pub begin: ::prost::alloc::string::String,
    /// 结束时间，格式：04:00
    #[prost(string, tag = "2")]
    pub end: ::prost::alloc::string::String,
}
/// DevObjType 设备对象定义
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct DevObjType {
    /// 设备模型缩写，如：MGC/CCU/PDU
    #[prost(string, tag = "1")]
    pub dev_type: ::prost::alloc::string::String,
    /// 设备类型下索引
    #[prost(string, tag = "2")]
    pub index: ::prost::alloc::string::String,
    /// 软件版本号
    #[prost(string, tag = "3")]
    pub version: ::prost::alloc::string::String,
}
/// TaskDescType 任务描述定义
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct TaskDescType {
    /// 设备信息
    #[prost(message, optional, tag = "1")]
    pub dev_info: ::core::option::Option<DevObjType>,
    /// 升级包文件名称
    #[prost(string, tag = "2")]
    pub pkg_name: ::prost::alloc::string::String,
    /// 升级包版本
    #[prost(string, tag = "3")]
    pub pkg_version: ::prost::alloc::string::String,
    /// 升级包校验码，sha256
    #[prost(string, tag = "4")]
    pub check_sum: ::prost::alloc::string::String,
}
/// DownloadInfo 下载对象定义
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct DownloadInfo {
    /// 下载方式枚举
    #[prost(enumeration = "DownloadType", tag = "1")]
    pub r#type: i32,
    /// 完整的下载地址路径，包含包名称
    #[prost(string, tag = "2")]
    pub pkg_url: ::prost::alloc::string::String,
    /// 用户名
    #[prost(string, tag = "3")]
    pub user: ::prost::alloc::string::String,
    /// 密码
    #[prost(string, tag = "4")]
    pub password: ::prost::alloc::string::String,
}
/// TaskType 任务对象
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct TaskType {
    /// 任务标识
    #[prost(string, tag = "1")]
    pub task_id: ::prost::alloc::string::String,
    /// 绑定任务标识
    #[prost(string, tag = "2")]
    pub bind_task_id: ::prost::alloc::string::String,
    /// 升级方式枚举
    #[prost(enumeration = "UpgradeType", tag = "3")]
    pub upgrade_type: i32,
    /// 任务动作枚举
    #[prost(enumeration = "TaskAct", tag = "4")]
    pub task_act: i32,
    /// 是否重启
    #[prost(bool, tag = "5")]
    pub if_reboot: bool,
    /// 执行空闲升级的时间段
    #[prost(message, repeated, tag = "6")]
    pub time_span_list: ::prost::alloc::vec::Vec<TimeSpanType>,
    /// 任务描述
    #[prost(message, optional, tag = "7")]
    pub task_info: ::core::option::Option<TaskDescType>,
    /// 升级包下载信息
    #[prost(message, optional, tag = "8")]
    pub download_info: ::core::option::Option<DownloadInfo>,
}
/// CmdInfo 控制指令信息
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct CmdInfo {
    /// 控制指令类型，参见指令类型枚举
    #[prost(string, tag = "1")]
    pub cmd_type: ::prost::alloc::string::String,
    /// 控制指令参数
    #[prost(string, repeated, tag = "2")]
    pub args: ::prost::alloc::vec::Vec<::prost::alloc::string::String>,
}
/// Telemetry 遥测数据
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct Telemetry {
    #[prost(string, tag = "1")]
    pub addr: ::prost::alloc::string::String,
    #[prost(double, tag = "2")]
    pub value: f64,
}
/// StateOrSignal 状态或者遥信
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct StateOrSignal {
    #[prost(string, tag = "1")]
    pub addr: ::prost::alloc::string::String,
    #[prost(int64, tag = "2")]
    pub value: i64,
}
/// StringData 字符数据
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct StringData {
    #[prost(string, tag = "1")]
    pub addr: ::prost::alloc::string::String,
    #[prost(string, tag = "2")]
    pub value: ::prost::alloc::string::String,
}
/// DownloadType 下载方式枚举
#[derive(Clone, Copy, Debug, PartialEq, Eq, Hash, PartialOrd, Ord, ::prost::Enumeration)]
#[repr(i32)]
pub enum DownloadType {
    /// 默认下载方式
    DefaultDownloadType = 0,
    /// FTP下载
    Ftp = 1,
    /// HTTPS下载
    Https = 2,
    /// FTPS下载
    Ftps = 3,
}
impl DownloadType {
    /// String value of the enum field names used in the ProtoBuf definition.
    ///
    /// The values are not transformed in any way and thus are considered stable
    /// (if the ProtoBuf definition does not change) and safe for programmatic use.
    pub fn as_str_name(&self) -> &'static str {
        match self {
            DownloadType::DefaultDownloadType => "DEFAULT_DOWNLOAD_TYPE",
            DownloadType::Ftp => "FTP",
            DownloadType::Https => "HTTPS",
            DownloadType::Ftps => "FTPS",
        }
    }
    /// Creates an enum from field names used in the ProtoBuf definition.
    pub fn from_str_name(value: &str) -> ::core::option::Option<Self> {
        match value {
            "DEFAULT_DOWNLOAD_TYPE" => Some(Self::DefaultDownloadType),
            "FTP" => Some(Self::Ftp),
            "HTTPS" => Some(Self::Https),
            "FTPS" => Some(Self::Ftps),
            _ => None,
        }
    }
}
/// UpgradeType 升级方式枚举
#[derive(Clone, Copy, Debug, PartialEq, Eq, Hash, PartialOrd, Ord, ::prost::Enumeration)]
#[repr(i32)]
pub enum UpgradeType {
    /// 默认升级方式
    DefaultUpgradeType = 0,
    /// 自动空闲升级
    Auto = 1,
    /// 半自动升级
    SemiAuto = 2,
    /// 手动升级
    Manual = 3,
    /// 新控制器拉取程序包
    NewCtrl = 4,
    /// 紧急强制立即升级
    Emergency = 5,
}
impl UpgradeType {
    /// String value of the enum field names used in the ProtoBuf definition.
    ///
    /// The values are not transformed in any way and thus are considered stable
    /// (if the ProtoBuf definition does not change) and safe for programmatic use.
    pub fn as_str_name(&self) -> &'static str {
        match self {
            UpgradeType::DefaultUpgradeType => "DEFAULT_UPGRADE_TYPE",
            UpgradeType::Auto => "AUTO",
            UpgradeType::SemiAuto => "SEMI_AUTO",
            UpgradeType::Manual => "MANUAL",
            UpgradeType::NewCtrl => "NEW_CTRL",
            UpgradeType::Emergency => "EMERGENCY",
        }
    }
    /// Creates an enum from field names used in the ProtoBuf definition.
    pub fn from_str_name(value: &str) -> ::core::option::Option<Self> {
        match value {
            "DEFAULT_UPGRADE_TYPE" => Some(Self::DefaultUpgradeType),
            "AUTO" => Some(Self::Auto),
            "SEMI_AUTO" => Some(Self::SemiAuto),
            "MANUAL" => Some(Self::Manual),
            "NEW_CTRL" => Some(Self::NewCtrl),
            "EMERGENCY" => Some(Self::Emergency),
            _ => None,
        }
    }
}
/// TaskAct 任务动作枚举
#[derive(Clone, Copy, Debug, PartialEq, Eq, Hash, PartialOrd, Ord, ::prost::Enumeration)]
#[repr(i32)]
pub enum TaskAct {
    /// 默认任务动作
    DefaultTaskAct = 0,
    /// 开始
    Start = 1,
    /// 取消
    Cancel = 2,
}
impl TaskAct {
    /// String value of the enum field names used in the ProtoBuf definition.
    ///
    /// The values are not transformed in any way and thus are considered stable
    /// (if the ProtoBuf definition does not change) and safe for programmatic use.
    pub fn as_str_name(&self) -> &'static str {
        match self {
            TaskAct::DefaultTaskAct => "DEFAULT_TASK_ACT",
            TaskAct::Start => "START",
            TaskAct::Cancel => "CANCEL",
        }
    }
    /// Creates an enum from field names used in the ProtoBuf definition.
    pub fn from_str_name(value: &str) -> ::core::option::Option<Self> {
        match value {
            "DEFAULT_TASK_ACT" => Some(Self::DefaultTaskAct),
            "START" => Some(Self::Start),
            "CANCEL" => Some(Self::Cancel),
            _ => None,
        }
    }
}
/// LoginResult 登录结果枚举
#[derive(Clone, Copy, Debug, PartialEq, Eq, Hash, PartialOrd, Ord, ::prost::Enumeration)]
#[repr(i32)]
pub enum LoginResult {
    /// 默认登录结果
    DefaultLoginResult = 0,
    /// 成功
    LoginSuccess = 1,
    /// 失败-重复上线
    LoginFailedDupLogin = 2,
    /// 失败-非法设备
    LoginFailedInvalidDev = 3,
    /// 失败-未知原因
    LoginFailedUnknown = 255,
}
impl LoginResult {
    /// String value of the enum field names used in the ProtoBuf definition.
    ///
    /// The values are not transformed in any way and thus are considered stable
    /// (if the ProtoBuf definition does not change) and safe for programmatic use.
    pub fn as_str_name(&self) -> &'static str {
        match self {
            LoginResult::DefaultLoginResult => "DEFAULT_LOGIN_RESULT",
            LoginResult::LoginSuccess => "LOGIN_SUCCESS",
            LoginResult::LoginFailedDupLogin => "LOGIN_FAILED_DUP_LOGIN",
            LoginResult::LoginFailedInvalidDev => "LOGIN_FAILED_INVALID_DEV",
            LoginResult::LoginFailedUnknown => "LOGIN_FAILED_UNKNOWN",
        }
    }
    /// Creates an enum from field names used in the ProtoBuf definition.
    pub fn from_str_name(value: &str) -> ::core::option::Option<Self> {
        match value {
            "DEFAULT_LOGIN_RESULT" => Some(Self::DefaultLoginResult),
            "LOGIN_SUCCESS" => Some(Self::LoginSuccess),
            "LOGIN_FAILED_DUP_LOGIN" => Some(Self::LoginFailedDupLogin),
            "LOGIN_FAILED_INVALID_DEV" => Some(Self::LoginFailedInvalidDev),
            "LOGIN_FAILED_UNKNOWN" => Some(Self::LoginFailedUnknown),
            _ => None,
        }
    }
}
/// ModelSummarySendReason 模型摘要信息上送原因
#[derive(Clone, Copy, Debug, PartialEq, Eq, Hash, PartialOrd, Ord, ::prost::Enumeration)]
#[repr(i32)]
pub enum ModelSummarySendReason {
    /// 默认模型摘要信息上送原因
    DefaultModelSummarySendReason = 0,
    /// 上线初次发送
    Login = 1,
    /// 模型发生变更
    ModelChanged = 2,
    /// 周期上送
    Period = 3,
}
impl ModelSummarySendReason {
    /// String value of the enum field names used in the ProtoBuf definition.
    ///
    /// The values are not transformed in any way and thus are considered stable
    /// (if the ProtoBuf definition does not change) and safe for programmatic use.
    pub fn as_str_name(&self) -> &'static str {
        match self {
            ModelSummarySendReason::DefaultModelSummarySendReason => {
                "DEFAULT_MODEL_SUMMARY_SEND_REASON"
            }
            ModelSummarySendReason::Login => "LOGIN",
            ModelSummarySendReason::ModelChanged => "MODEL_CHANGED",
            ModelSummarySendReason::Period => "PERIOD",
        }
    }
    /// Creates an enum from field names used in the ProtoBuf definition.
    pub fn from_str_name(value: &str) -> ::core::option::Option<Self> {
        match value {
            "DEFAULT_MODEL_SUMMARY_SEND_REASON" => Some(Self::DefaultModelSummarySendReason),
            "LOGIN" => Some(Self::Login),
            "MODEL_CHANGED" => Some(Self::ModelChanged),
            "PERIOD" => Some(Self::Period),
            _ => None,
        }
    }
}
/// AlarmReason 告警原因枚举
#[derive(Clone, Copy, Debug, PartialEq, Eq, Hash, PartialOrd, Ord, ::prost::Enumeration)]
#[repr(i32)]
pub enum AlarmReason {
    /// 默认告警原因
    DefaultAlarmReason = 0,
    /// 发送
    Active = 1,
    /// 恢复
    Resume = 2,
}
impl AlarmReason {
    /// String value of the enum field names used in the ProtoBuf definition.
    ///
    /// The values are not transformed in any way and thus are considered stable
    /// (if the ProtoBuf definition does not change) and safe for programmatic use.
    pub fn as_str_name(&self) -> &'static str {
        match self {
            AlarmReason::DefaultAlarmReason => "DEFAULT_ALARM_REASON",
            AlarmReason::Active => "ACTIVE",
            AlarmReason::Resume => "RESUME",
        }
    }
    /// Creates an enum from field names used in the ProtoBuf definition.
    pub fn from_str_name(value: &str) -> ::core::option::Option<Self> {
        match value {
            "DEFAULT_ALARM_REASON" => Some(Self::DefaultAlarmReason),
            "ACTIVE" => Some(Self::Active),
            "RESUME" => Some(Self::Resume),
            _ => None,
        }
    }
}
/// UpgradeStatus 升级状态枚举
#[derive(Clone, Copy, Debug, PartialEq, Eq, Hash, PartialOrd, Ord, ::prost::Enumeration)]
#[repr(i32)]
pub enum UpgradeStatus {
    /// 初始化升级
    InitUpgrade = 0,
    /// 等待下载
    WaitingDownload = 1,
    /// 下载中
    Downloading = 2,
    /// 准备升级
    ReadyUpgrade = 3,
    /// 等待设备空闲
    WaitingDevIdle = 4,
    /// 升级中
    Upgrading = 5,
    /// 等待设备重启
    WaitingDevReboot = 6,
    /// 等待升级结果
    WaitingUpgradeResult = 7,
    /// 升级成功
    UpgradeSuccess = 8,
    /// 升级失败
    UpgradeFailed = 9,
    /// 任务丢失
    TaskLost = 99,
}
impl UpgradeStatus {
    /// String value of the enum field names used in the ProtoBuf definition.
    ///
    /// The values are not transformed in any way and thus are considered stable
    /// (if the ProtoBuf definition does not change) and safe for programmatic use.
    pub fn as_str_name(&self) -> &'static str {
        match self {
            UpgradeStatus::InitUpgrade => "INIT_UPGRADE",
            UpgradeStatus::WaitingDownload => "WAITING_DOWNLOAD",
            UpgradeStatus::Downloading => "DOWNLOADING",
            UpgradeStatus::ReadyUpgrade => "READY_UPGRADE",
            UpgradeStatus::WaitingDevIdle => "WAITING_DEV_IDLE",
            UpgradeStatus::Upgrading => "UPGRADING",
            UpgradeStatus::WaitingDevReboot => "WAITING_DEV_REBOOT",
            UpgradeStatus::WaitingUpgradeResult => "WAITING_UPGRADE_RESULT",
            UpgradeStatus::UpgradeSuccess => "UPGRADE_SUCCESS",
            UpgradeStatus::UpgradeFailed => "UPGRADE_FAILED",
            UpgradeStatus::TaskLost => "TASK_LOST",
        }
    }
    /// Creates an enum from field names used in the ProtoBuf definition.
    pub fn from_str_name(value: &str) -> ::core::option::Option<Self> {
        match value {
            "INIT_UPGRADE" => Some(Self::InitUpgrade),
            "WAITING_DOWNLOAD" => Some(Self::WaitingDownload),
            "DOWNLOADING" => Some(Self::Downloading),
            "READY_UPGRADE" => Some(Self::ReadyUpgrade),
            "WAITING_DEV_IDLE" => Some(Self::WaitingDevIdle),
            "UPGRADING" => Some(Self::Upgrading),
            "WAITING_DEV_REBOOT" => Some(Self::WaitingDevReboot),
            "WAITING_UPGRADE_RESULT" => Some(Self::WaitingUpgradeResult),
            "UPGRADE_SUCCESS" => Some(Self::UpgradeSuccess),
            "UPGRADE_FAILED" => Some(Self::UpgradeFailed),
            "TASK_LOST" => Some(Self::TaskLost),
            _ => None,
        }
    }
}
/// FileOpType 文件操作类型枚举
#[derive(Clone, Copy, Debug, PartialEq, Eq, Hash, PartialOrd, Ord, ::prost::Enumeration)]
#[repr(i32)]
pub enum FileOpType {
    /// 默认文件操作类型
    DefaultFileOpType = 0,
    /// 上传
    Upload = 1,
    /// 下载
    Download = 2,
}
impl FileOpType {
    /// String value of the enum field names used in the ProtoBuf definition.
    ///
    /// The values are not transformed in any way and thus are considered stable
    /// (if the ProtoBuf definition does not change) and safe for programmatic use.
    pub fn as_str_name(&self) -> &'static str {
        match self {
            FileOpType::DefaultFileOpType => "DEFAULT_FILE_OP_TYPE",
            FileOpType::Upload => "UPLOAD",
            FileOpType::Download => "DOWNLOAD",
        }
    }
    /// Creates an enum from field names used in the ProtoBuf definition.
    pub fn from_str_name(value: &str) -> ::core::option::Option<Self> {
        match value {
            "DEFAULT_FILE_OP_TYPE" => Some(Self::DefaultFileOpType),
            "UPLOAD" => Some(Self::Upload),
            "DOWNLOAD" => Some(Self::Download),
            _ => None,
        }
    }
}
/// FileProcessingStatus 文件操作状态枚举
#[derive(Clone, Copy, Debug, PartialEq, Eq, Hash, PartialOrd, Ord, ::prost::Enumeration)]
#[repr(i32)]
pub enum FileProcessingStatus {
    /// 默认文件操作状态
    DefaultFileProcessingStatus = 0,
    /// 已收到任务
    Received = 1,
    /// 执行中
    Processing = 2,
    /// 执行成功
    ProcessOk = 3,
    /// 执行失败
    ProcessFailed = 4,
}
impl FileProcessingStatus {
    /// String value of the enum field names used in the ProtoBuf definition.
    ///
    /// The values are not transformed in any way and thus are considered stable
    /// (if the ProtoBuf definition does not change) and safe for programmatic use.
    pub fn as_str_name(&self) -> &'static str {
        match self {
            FileProcessingStatus::DefaultFileProcessingStatus => "DEFAULT_FILE_PROCESSING_STATUS",
            FileProcessingStatus::Received => "RECEIVED",
            FileProcessingStatus::Processing => "PROCESSING",
            FileProcessingStatus::ProcessOk => "PROCESS_OK",
            FileProcessingStatus::ProcessFailed => "PROCESS_FAILED",
        }
    }
    /// Creates an enum from field names used in the ProtoBuf definition.
    pub fn from_str_name(value: &str) -> ::core::option::Option<Self> {
        match value {
            "DEFAULT_FILE_PROCESSING_STATUS" => Some(Self::DefaultFileProcessingStatus),
            "RECEIVED" => Some(Self::Received),
            "PROCESSING" => Some(Self::Processing),
            "PROCESS_OK" => Some(Self::ProcessOk),
            "PROCESS_FAILED" => Some(Self::ProcessFailed),
            _ => None,
        }
    }
}
/// Result 结果枚举
#[derive(Clone, Copy, Debug, PartialEq, Eq, Hash, PartialOrd, Ord, ::prost::Enumeration)]
#[repr(i32)]
pub enum Result {
    /// 默认结果
    DefaultResult = 0,
    /// 成功
    Success = 1,
    /// 失败
    Failed = 2,
}
impl Result {
    /// String value of the enum field names used in the ProtoBuf definition.
    ///
    /// The values are not transformed in any way and thus are considered stable
    /// (if the ProtoBuf definition does not change) and safe for programmatic use.
    pub fn as_str_name(&self) -> &'static str {
        match self {
            Result::DefaultResult => "DEFAULT_RESULT",
            Result::Success => "SUCCESS",
            Result::Failed => "FAILED",
        }
    }
    /// Creates an enum from field names used in the ProtoBuf definition.
    pub fn from_str_name(value: &str) -> ::core::option::Option<Self> {
        match value {
            "DEFAULT_RESULT" => Some(Self::DefaultResult),
            "SUCCESS" => Some(Self::Success),
            "FAILED" => Some(Self::Failed),
            _ => None,
        }
    }
}
/// MsgType 消息类型枚举
#[derive(Clone, Copy, Debug, PartialEq, Eq, Hash, PartialOrd, Ord, ::prost::Enumeration)]
#[repr(i32)]
pub enum MsgType {
    /// 默认消息类型
    DefaultMsgType = 0,
    /// 登录请求
    LoginReq = 1,
    /// 登录应答
    LoginAns = 2,
    /// 模型摘要信息请求
    ModelSummaryReq = 3,
    /// 模型摘要信息应答
    ModelSummaryAns = 4,
    /// 模型信息召唤
    ModelDetailCall = 5,
    /// 模型信息上送
    ModelDetailReq = 6,
    /// 模型信息应答
    ModelDetailAns = 7,
    /// 告警突发上送
    AlarmActiveReq = 8,
    /// 告警突发应答
    AlarmActiveAns = 9,
    /// 告警信息召唤
    AlarmCall = 10,
    /// 告警信息应答
    AlarmAns = 11,
    /// 实时数据召唤
    RtDataCall = 12,
    /// 实时数据应答
    RtDataAns = 13,
    /// 升级任务下发请求
    UpgradeReq = 14,
    /// 升级状态召唤
    UpgradeStatusCall = 15,
    /// 升级状态应答
    UpgradeStatusAns = 16,
    /// 配置文件列表召唤
    ConfigFileListCall = 17,
    /// 配置文件列表应答
    ConfigFileListAns = 18,
    /// 文件摘要召唤
    FileSummaryCall = 19,
    /// 文件摘要应答
    FileSummaryAns = 20,
    /// 文件操作请求
    FileProcessingReq = 21,
    /// 文件操作应答
    FileProcessingAns = 22,
    /// 远程控制请求
    RemoteControlReq = 23,
    /// 远程控制应答
    RemoteControlAns = 24,
    /// 升级任务下发应答
    UpgradeAns = 25,
}
impl MsgType {
    /// String value of the enum field names used in the ProtoBuf definition.
    ///
    /// The values are not transformed in any way and thus are considered stable
    /// (if the ProtoBuf definition does not change) and safe for programmatic use.
    pub fn as_str_name(&self) -> &'static str {
        match self {
            MsgType::DefaultMsgType => "DEFAULT_MSG_TYPE",
            MsgType::LoginReq => "LOGIN_REQ",
            MsgType::LoginAns => "LOGIN_ANS",
            MsgType::ModelSummaryReq => "MODEL_SUMMARY_REQ",
            MsgType::ModelSummaryAns => "MODEL_SUMMARY_ANS",
            MsgType::ModelDetailCall => "MODEL_DETAIL_CALL",
            MsgType::ModelDetailReq => "MODEL_DETAIL_REQ",
            MsgType::ModelDetailAns => "MODEL_DETAIL_ANS",
            MsgType::AlarmActiveReq => "ALARM_ACTIVE_REQ",
            MsgType::AlarmActiveAns => "ALARM_ACTIVE_ANS",
            MsgType::AlarmCall => "ALARM_CALL",
            MsgType::AlarmAns => "ALARM_ANS",
            MsgType::RtDataCall => "RT_DATA_CALL",
            MsgType::RtDataAns => "RT_DATA_ANS",
            MsgType::UpgradeReq => "UPGRADE_REQ",
            MsgType::UpgradeStatusCall => "UPGRADE_STATUS_CALL",
            MsgType::UpgradeStatusAns => "UPGRADE_STATUS_ANS",
            MsgType::ConfigFileListCall => "CONFIG_FILE_LIST_CALL",
            MsgType::ConfigFileListAns => "CONFIG_FILE_LIST_ANS",
            MsgType::FileSummaryCall => "FILE_SUMMARY_CALL",
            MsgType::FileSummaryAns => "FILE_SUMMARY_ANS",
            MsgType::FileProcessingReq => "FILE_PROCESSING_REQ",
            MsgType::FileProcessingAns => "FILE_PROCESSING_ANS",
            MsgType::RemoteControlReq => "REMOTE_CONTROL_REQ",
            MsgType::RemoteControlAns => "REMOTE_CONTROL_ANS",
            MsgType::UpgradeAns => "UPGRADE_ANS",
        }
    }
    /// Creates an enum from field names used in the ProtoBuf definition.
    pub fn from_str_name(value: &str) -> ::core::option::Option<Self> {
        match value {
            "DEFAULT_MSG_TYPE" => Some(Self::DefaultMsgType),
            "LOGIN_REQ" => Some(Self::LoginReq),
            "LOGIN_ANS" => Some(Self::LoginAns),
            "MODEL_SUMMARY_REQ" => Some(Self::ModelSummaryReq),
            "MODEL_SUMMARY_ANS" => Some(Self::ModelSummaryAns),
            "MODEL_DETAIL_CALL" => Some(Self::ModelDetailCall),
            "MODEL_DETAIL_REQ" => Some(Self::ModelDetailReq),
            "MODEL_DETAIL_ANS" => Some(Self::ModelDetailAns),
            "ALARM_ACTIVE_REQ" => Some(Self::AlarmActiveReq),
            "ALARM_ACTIVE_ANS" => Some(Self::AlarmActiveAns),
            "ALARM_CALL" => Some(Self::AlarmCall),
            "ALARM_ANS" => Some(Self::AlarmAns),
            "RT_DATA_CALL" => Some(Self::RtDataCall),
            "RT_DATA_ANS" => Some(Self::RtDataAns),
            "UPGRADE_REQ" => Some(Self::UpgradeReq),
            "UPGRADE_STATUS_CALL" => Some(Self::UpgradeStatusCall),
            "UPGRADE_STATUS_ANS" => Some(Self::UpgradeStatusAns),
            "CONFIG_FILE_LIST_CALL" => Some(Self::ConfigFileListCall),
            "CONFIG_FILE_LIST_ANS" => Some(Self::ConfigFileListAns),
            "FILE_SUMMARY_CALL" => Some(Self::FileSummaryCall),
            "FILE_SUMMARY_ANS" => Some(Self::FileSummaryAns),
            "FILE_PROCESSING_REQ" => Some(Self::FileProcessingReq),
            "FILE_PROCESSING_ANS" => Some(Self::FileProcessingAns),
            "REMOTE_CONTROL_REQ" => Some(Self::RemoteControlReq),
            "REMOTE_CONTROL_ANS" => Some(Self::RemoteControlAns),
            "UPGRADE_ANS" => Some(Self::UpgradeAns),
            _ => None,
        }
    }
}
/// MeasureReason 量测传输原因
#[derive(Clone, Copy, Debug, PartialEq, Eq, Hash, PartialOrd, Ord, ::prost::Enumeration)]
#[repr(i32)]
pub enum MeasureReason {
    /// 默认
    DefaultMeasureReason = 0,
    /// 响应召唤
    CallAns = 1,
    /// 变化上送
    DataChanged = 2,
}
impl MeasureReason {
    /// String value of the enum field names used in the ProtoBuf definition.
    ///
    /// The values are not transformed in any way and thus are considered stable
    /// (if the ProtoBuf definition does not change) and safe for programmatic use.
    pub fn as_str_name(&self) -> &'static str {
        match self {
            MeasureReason::DefaultMeasureReason => "DEFAULT_MEASURE_REASON",
            MeasureReason::CallAns => "CALL_ANS",
            MeasureReason::DataChanged => "DATA_CHANGED",
        }
    }
    /// Creates an enum from field names used in the ProtoBuf definition.
    pub fn from_str_name(value: &str) -> ::core::option::Option<Self> {
        match value {
            "DEFAULT_MEASURE_REASON" => Some(Self::DefaultMeasureReason),
            "CALL_ANS" => Some(Self::CallAns),
            "DATA_CHANGED" => Some(Self::DataChanged),
            _ => None,
        }
    }
}
