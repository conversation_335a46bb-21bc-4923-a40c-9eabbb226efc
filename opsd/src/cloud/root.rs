use std::{
    collections::{HashMap, HashSet},
    ffi::OsStr,
    sync::{Arc, Mutex},
    time::Duration,
};

use sysinfo::{Pid, ProcessRefreshKind, RefreshKind};

use crate::{
    cloud::{
        mgapp::ProcessOrNone,
        proto::{
            mgcops::{Alarm, Device, MeasureReason, RemoteControlReq, RtDataAns},
            NetApprochMethod,
        },
    },
    system::{self, tools::MMCHealth},
};

pub struct SystemMonitor {
    hardware: system::Hardware,
    sysinfo: sysinfo::System,
    pub(crate) mmcinfo: Option<MMCHealth>,
    pids: HashMap<String, Option<Pid>>,
}

struct HardwareCapability {
    cpu_arch: String,
    software_version: String,
    mem_cap: u64,
    disk_cap: u64,
}

struct SystemOverview {
    cpu_usage: f32,
    mem_used: u32,
    disk_used: u32,
    network_type: NetApprochMethod,
}

impl SystemMonitor {
    pub fn new() -> Self {
        let mut s = Self {
            hardware: system::Hardware::get().unwrap_or_default(),
            sysinfo: sysinfo::System::new(),
            pids: HashMap::new(),
            mmcinfo: None,
        };
        sysinfo::set_open_files_limit(10);
        s.refresh_system();
        s.refresh_mmc();
        s.refresh_all_processes();
        s
    }

    pub fn refresh_system(&mut self) {
        self.sysinfo
            .refresh_specifics(RefreshKind::everything().without_processes());
    }

    pub fn refresh_mmc(&mut self) {
        if self.hardware.soc.eq("OKA40i") {
            self.mmcinfo = system::tools::get_mmcinfo(Duration::from_secs(60 * 60)).ok();
        }
    }

    pub fn refresh_all_processes(&mut self) -> Vec<&str> {
        self.sysinfo.refresh_processes_specifics(
            sysinfo::ProcessesToUpdate::All,
            true,
            ProcessRefreshKind::nothing().with_cpu().with_memory(),
        );

        //find changed processes
        let mut changed = vec![];
        for ele in self.pids.iter_mut() {
            if ele.1.is_some() && self.sysinfo.process(ele.1.clone().unwrap()).is_some() {
                continue;
            }
            if let Some(p) = self
                .sysinfo
                .processes_by_exact_name(OsStr::new(ele.0.as_str()))
                .next()
            {
                *ele.1 = Some(p.pid());
                changed.push(ele.0.as_str());
            }
        }
        changed
    }

    pub fn update(&mut self, programs: &[&str]) -> Vec<&str> {
        self.pids.retain(|k, _| programs.contains(&k.as_str()));

        let mut changed = HashSet::new();
        if self.pids.len() != programs.len() {
            for &ele in programs {
                if !self.pids.get(ele).is_some() {
                    self.pids.insert(ele.to_string(), None);
                    changed.insert(ele);
                }
            }
        }

        self.pids
            .keys()
            .filter_map(|e| changed.contains(e.as_str()).then(|| e.as_str()))
            .collect()
    }

    pub fn refresh_specifics(&mut self, names: &[&str]) -> Vec<String> {
        let added = self
            .update(names)
            .iter()
            .map(|e| e.to_string())
            .collect::<Vec<_>>();
        let changed = self
            .refresh_all_processes()
            .iter()
            .map(|e| e.to_string())
            .collect::<Vec<_>>();
        changed
            .into_iter()
            .chain(added.into_iter())
            .collect::<HashSet<_>>()
            .into_iter()
            .collect()
    }

    pub fn get_processes<'a>(&'a self, names: &[&str]) -> Vec<ProcessOrNone<'a>> {
        names
            .iter()
            .map(|&n| {
                match match self.pids.get(n) {
                    Some(pid) => match pid {
                        Some(pid) => self.sysinfo.process(pid.clone()).into_iter().next(),
                        None => None,
                    },
                    None => self
                        .sysinfo
                        .processes_by_exact_name(OsStr::new(n))
                        .into_iter()
                        .next(),
                } {
                    Some(p) => ProcessOrNone::Process(p),
                    None => ProcessOrNone::None(n.to_string()),
                }
            })
            .collect()
    }

    fn get_mmc_health(&self) -> Option<MMCHealth> {
        self.mmcinfo.clone()
    }

    fn get_hardware_capability(&self) -> HardwareCapability {
        HardwareCapability {
            cpu_arch: sysinfo::System::cpu_arch(),
            software_version: system::get_os_soft_version().map_or("".to_string(), |e| e.0),
            mem_cap: self.sysinfo.total_memory(),
            disk_cap: {
                let mut disk = sysinfo::Disks::new_with_refreshed_list();
                disk.refresh(true);
                disk.iter()
                    .find(|e| e.mount_point() == std::path::Path::new("/"))
                    .map_or(0, |e| e.total_space())
            },
        }
    }

    fn get_system_overview(&self) -> SystemOverview {
        SystemOverview {
            cpu_usage: self.sysinfo.global_cpu_usage(),
            mem_used: self.sysinfo.used_memory() as u32,
            disk_used: {
                let mut disk = sysinfo::Disks::new_with_refreshed_list();
                disk.refresh(true);
                disk.iter()
                    .find(|e| e.mount_point() == std::path::Path::new("/"))
                    .map_or(0, |e| e.total_space() - e.available_space())
            } as u32,
            network_type: {
                match crate::system::network::get_default_net_interface() {
                    Ok(iface) => {
                        if iface.starts_with("eth") {
                            NetApprochMethod::Router
                        } else if iface.starts_with("usb") {
                            NetApprochMethod::Celluar
                        } else {
                            NetApprochMethod::Unknown
                        }
                    }
                    Err(_) => NetApprochMethod::Unknown,
                }
            },
        }
    }
}

pub(crate) struct Root {
    sn: String,
    pub(crate) monitor: Arc<Mutex<SystemMonitor>>,
}

enum SystemCommand {
    RestartSystem,
    RestartVPN,
    RestartWeb,
    Custom,
}

impl TryFrom<&str> for SystemCommand {
    type Error = anyhow::Error;

    fn try_from(value: &str) -> Result<Self, Self::Error> {
        match value {
            "RestartSystem" => Ok(SystemCommand::RestartSystem),
            "RestartVPN" => Ok(SystemCommand::RestartVPN),
            "RestartWeb" => Ok(SystemCommand::RestartWeb),
            "Custom" => Ok(SystemCommand::Custom),
            &_ => Err(anyhow::anyhow!("Unknown system command: {}", value)),
        }
    }
}

impl Root {
    pub fn new() -> Root {
        let sys = SystemMonitor::new();
        let sys_shared = Arc::new(Mutex::new(sys));
        Root {
            sn: crate::system::sn_get().unwrap(),
            monitor: sys_shared,
        }
    }

    pub fn device(&self) -> Device {
        let hwinfo = crate::system::Hardware::get().unwrap_or_default();
        let cap = self.monitor.lock().unwrap().get_hardware_capability();
        Device {
            dev_type: "MGC".to_string(),
            r#virtual: false,
            sn: self.sn.clone(),
            index: self.sn.clone(),
            version: "".to_string(),
            props: HashMap::from([
                ("software_version".into(), cap.software_version),
                ("cpu_spec".into(), cap.cpu_arch),
                (
                    "os".into(),
                    sysinfo::System::os_version().unwrap_or("".to_string()),
                ),
                ("mem_cap".into(), (cap.mem_cap / (1024 * 1024)).to_string()),
                (
                    "disk_cap".into(),
                    (cap.disk_cap / (1024 * 1024)).to_string(),
                ),
            ]),
            children: vec![],
            vendor: "DERI".to_string(),
            model: hwinfo.board,
        }
    }

    pub fn get_telemetries(&self) -> RtDataAns {
        let ov = self.monitor.lock().unwrap().get_system_overview();
        let ps: u32 = ov.network_type.into();
        let load_avg = sysinfo::System::load_average().fifteen;
        let mut sys_metrics = vec![
            ("cpu_usage", serde_json::json!(ov.cpu_usage)),
            ("mem_used", serde_json::json!(ov.mem_used / (1024 * 1024))),
            ("disk_used", serde_json::json!(ov.disk_used / (1024 * 1024))),
            ("average_load", serde_json::json!(load_avg)),
            ("network_type", serde_json::json!(ps)),
        ];

        if let Some(h) = self.monitor.lock().unwrap().get_mmc_health() {
            sys_metrics.extend([
                ("mmc_erase_count", serde_json::json!(h.erase_count.0)),
                ("mmc_erase_count_max", serde_json::json!(h.erase_count.1)),
                (
                    "mmc_write_amplification",
                    serde_json::json!(h.write_amplification),
                ),
                ("mmc_bad_block_count", serde_json::json!(h.bad_block_count)),
                (
                    "mmc_disk_write_capacity",
                    serde_json::json!(h.disk_write_capacity),
                ),
                (
                    "mmc_last_check_interval_write",
                    serde_json::json!(h.last_check_interval_write),
                ),
            ]);
        }

        match crate::system::network::cellular_info() {
            Ok(metrics) => {
                sys_metrics.extend([
                    ("iot_rsrp", serde_json::json!(metrics.rsrp)),
                    ("iot_snr", serde_json::json!(metrics.sinr)),
                    ("iot_module", serde_json::json!(metrics.model)),
                    ("iot_version", serde_json::json!(metrics.rev)),
                ]);
                if let Some(card) = metrics.card {
                    sys_metrics.extend([
                        ("iot_iccid", serde_json::json!(card.iccid)),
                        ("iot_carrier", serde_json::json!(card.operator)),
                        ("imsi", serde_json::json!(card.imsi)),
                        ("iot_mode", serde_json::json!(metrics.mode)),
                    ]);
                }
            }
            Err(_) => {}
        };

        let mut r: RtDataAns = sys_metrics.as_slice().into();
        r.reason = MeasureReason::CallAns.into();
        r.dev_type = "MGC".to_string();
        r.index = self.sn.clone();
        r
    }

    pub fn get_alarms(&self) -> anyhow::Result<Alarm> {
        Ok(Alarm {
            index: self.sn.clone(),
            dev_type: "MGC".to_string(),
            dev_alarm_list: vec![],
        })
    }

    pub async fn control(&self, action: &RemoteControlReq) -> anyhow::Result<()> {
        let cmd = action.cmd.as_ref().unwrap();
        match cmd.cmd_type.as_str().try_into()? {
            SystemCommand::RestartSystem => {
                log::info!("recv system reboot command,will reboot after 5 seconds...");
                tokio::spawn(async move {
                    tokio::time::sleep(std::time::Duration::from_secs(5)).await;
                    std::process::Command::new("reboot").spawn()?;
                    Ok::<_, anyhow::Error>(())
                });
                Ok(())
            }
            SystemCommand::RestartVPN => {
                if let Some(cmd) = system::tools::get("vpn") {
                    cmd.run_async(&["restart"]).await?;
                } else {
                    system::tools::restart_vpn_v2_0().await?;
                }
                Ok(())
            }
            SystemCommand::RestartWeb => {
                if let Some(cmd) = system::tools::get("embs") {
                    cmd.run_async(&["restart"]).await?;
                } else {
                    system::tools::restart_web_v2_0().await?;
                }
                Ok(())
            }
            SystemCommand::Custom => {
                if cmd.args.len() > 0 {
                    system::tools::get(cmd.args[0].as_str())
                        .ok_or(anyhow::anyhow!("command not found"))?
                        .run_async(&cmd.args[1..])
                        .await?;
                    Ok(())
                } else {
                    Err(anyhow::anyhow!("command not found"))
                }
            }
        }
    }

    pub async fn get_process_rtdata(&self, names: &[&str]) -> Vec<RtDataAns> {
        let sys = self.monitor.lock().unwrap();
        sys.get_processes(names).into_iter().map(|p|{p.into()}).collect()
    }
}
