/*
 * Copyright (c) 2024. DERI. Nanjing, China. All rights reserved
 * FILENAME: settings.rs
 * PROJECT: opsd
 * AUTHOR: shenshuangquan
 */

use crate::cloud::proto::mgcops;
use base64::Engine;
use tokio::fs::{File, OpenOptions};
use tokio::io::{AsyncReadExt, AsyncWriteExt};

const OPS_CONFIG_PATH: &str = "/ops/config";
const SETTINGS: [(&str, &str); 3] = [
    ("mgc", "/deri/mgc/config.lua"),
    ("opkg", "/etc/opkg/derifeeds.conf"),
    ("ntp", "/etc/ntp.conf"),
];

pub fn init_settings() {
    std::fs::create_dir_all(OPS_CONFIG_PATH).unwrap_or_default();
    for (name, path) in SETTINGS {
        let link_path = format!("{}/{}", OPS_CONFIG_PATH, name);
        if !std::path::Path::new(&link_path).exists() {
            _ = std::os::unix::fs::symlink(path, link_path);
        }
    }
}

pub async fn list() -> anyhow::Result<Vec<(String, String)>> {
    let mut files = vec![];
    let mut dir = tokio::fs::read_dir(OPS_CONFIG_PATH).await;
    if dir.is_err() {
        _ = std::fs::create_dir_all(OPS_CONFIG_PATH);
        dir = tokio::fs::read_dir(OPS_CONFIG_PATH).await;
    }
    let mut dir = dir?;
    while let Some(entry) = dir.next_entry().await? {
        if entry.file_type().await?.is_symlink() {
            let dest = tokio::fs::read_link(entry.path()).await?;
            files.push((
                entry.file_name().to_str().unwrap().to_string(),
                dest.to_str().unwrap().to_string(),
            ));
        }
    }
    Ok(files)
}

pub async fn download(path: &str, download: mgcops::DownloadInfo) -> Result<(), anyhow::Error> {
    let encoded = base64::engine::general_purpose::URL_SAFE
        .encode(format!("{}:{}", download.user, download.password));
    let h = reqwest::header::HeaderValue::from_str(&format!("Basic {}", encoded));
    let client = crate::cloud::get_request_client()?;
    let resp = client
        .get(download.pkg_url)
        .header(reqwest::header::AUTHORIZATION, h.unwrap())
        .send()
        .await?;
    let bt = resp.bytes().await?;
    let mut f = OpenOptions::new()
        .create(true)
        .truncate(true)
        .read(true)
        .write(true)
        .open(path)
        .await?;
    f.write(&bt).await?;
    Ok(())
}

pub async fn upload(path: &str, download: mgcops::DownloadInfo) -> Result<(), anyhow::Error> {
    let p = std::path::Path::new(path);
    let mut file = File::open(p).await?;
    let mut file_content = Vec::new();
    file.read_to_end(&mut file_content).await?;
    use base64::engine::general_purpose::URL_SAFE;
    let encoded = URL_SAFE.encode(format!("{}:{}", download.user, download.password));
    let header = reqwest::header::HeaderValue::from_str(&format!("Basic {}", encoded));
    let client = crate::cloud::get_request_client()?;
    let form = reqwest::multipart::Form::new().part(
        "file",
        reqwest::multipart::Part::bytes(file_content)
            .file_name(p.file_name().unwrap().to_str().unwrap().to_string()),
    );
    client
        .post(download.pkg_url)
        .header(reqwest::header::AUTHORIZATION, header.unwrap())
        .multipart(form)
        .send()
        .await?;
    Ok(())
}
