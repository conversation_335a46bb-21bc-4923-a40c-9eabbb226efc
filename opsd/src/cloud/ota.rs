/*
 * Copyright (c) 2024. DERI. Nanjing, China. All rights reserved
 * FILENAME: ota.rs
 * PROJECT: opsd
 * AUTHOR: shenshuangquan
 */

use crate::{cloud::proto::mgcops::UpgradeStatus, ota::OTA_TMP_DATA_PATH};
use std::path::{Path, PathBuf};

#[derive(Default)]
pub(super) struct TaskStateStore {
    path: PathBuf,
}

impl TaskStateStore {
    pub(super) fn new<T: AsRef<str>>(dev_type: T, index: T) -> TaskStateStore {
        let path = Path::new(OTA_TMP_DATA_PATH).join(format!(
            "models/{}.{}",
            dev_type.as_ref().to_lowercase(),
            index.as_ref().to_lowercase()
        ));
        if !path.exists() {
            _ = std::fs::create_dir_all(&path);
        }
        TaskStateStore { path }
    }

    pub(crate) fn set_task_status_by_id(id: &str, status: UpgradeStatus) {
        let path = std::path::Path::new(OTA_TMP_DATA_PATH)
            .join("tasks")
            .join(id)
            .join("status");
        if path.exists() {
            _ = std::fs::write(path, status.as_str_name());
        }
    }
    pub(super) fn task_id(&self) -> Option<String> {
        self.get("task_id")
    }

    pub(super) fn task_status(&self) -> Option<UpgradeStatus> {
        self.get("status")
            .map(|e| UpgradeStatus::from_str_name(&e))
            .flatten()
    }

    pub(super) fn set_task_status(&self, status: UpgradeStatus) {
        self.set("status", status.as_str_name())
    }

    pub(super) fn get_task_status(id: &str) -> Option<UpgradeStatus> {
        let path = std::path::Path::new(OTA_TMP_DATA_PATH)
            .join("tasks")
            .join(id);
        std::fs::read_to_string(path)
            .ok()
            .map(|e| UpgradeStatus::from_str_name(&e))
            .flatten()
    }

    pub(super) fn set_task_id(&self, id: &str) {
        self.set("task_id", id);
        let tasks_path = std::path::Path::new(OTA_TMP_DATA_PATH).join("tasks");
        if !tasks_path.exists() {
            _ = std::fs::create_dir_all(&tasks_path);
        }
        let task_path = tasks_path.join(id);
        if task_path.exists() {
            _ = std::fs::remove_file(&task_path);
        }
        if let Some(entry) = std::fs::read_dir(&tasks_path).unwrap().find(|e| {
            if let Ok(e) = e {
                std::fs::read_link(e.path()).is_ok_and(|e| e.eq(&self.path))
            } else {
                false
            }
        }) {
            _ = std::fs::rename(entry.unwrap().path(), task_path);
        } else {
            _ = std::os::unix::fs::symlink(&self.path, task_path);
        }
    }

    fn set(&self, attr: &str, val: impl AsRef<str>) {
        let attr_path = self.path.join(attr);
        let str = val.as_ref();
        _ = std::fs::write(attr_path, str);
    }

    fn get(&self, attr: &str) -> Option<String> {
        let attr_path = self.path.join(attr);
        std::fs::read_to_string(attr_path).ok()
    }
}
