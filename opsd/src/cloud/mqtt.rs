/*
 * Copyright (c) 2024. DERI. Nanjing, China. All rights reserved
 * FILENAME: mqtt.rs
 * PROJECT: opsd
 * AUTHOR: shenshuangquan
 */

use std::collections::HashMap;
use std::fmt::Debug;
use std::fs::File;
use std::io::Read;
use std::sync::atomic::AtomicBool;
use std::sync::atomic::Ordering::Relaxed;
use std::sync::Arc;
use std::time::Duration;

use anyhow::{anyhow, Context};
use log::{debug, info, warn};
use prost::Message;
use rumqttc::{
    AsyncClient, Event, EventLoop, Incoming, MqttOptions, QoS, SubscribeFilter, Transport,
};
use serde::Deserialize;
use tokio::sync::Mutex;
use tokio::time::Instant;

use crate::cloud::codec::Codec;
use crate::cloud::proto::mgcops::LoginResult::LoginSuccess;
use crate::cloud::proto::mgcops::{Frame, LoginAns, LoginReq, MsgType};
use crate::cloud::proto::{MessageType, Topic, TOPICS};
use crate::ota::resource::Authorization;
use crate::system::{self, sn_get};

pub struct MQTT {
    id: String,
    link_config: LinkConfig,
    event: Mutex<Option<EventLoop>>,
    sender: Mutex<Option<(AsyncClient, HashMap<Topic, String>)>>,
    codec: Arc<Codec>,
    login: AtomicBool,
}

#[derive(Debug, Deserialize)]
pub struct TLSConfig {
    ca: String,
    client: ClientTLSConfig,
}

impl TLSConfig {
    pub fn new_with_path(path: &str) -> Self {
        TLSConfig {
            ca: format!("{}/deri-service-ca.crt", path),
            client: ClientTLSConfig {
                key: format!("{}/device.key", path),
                cert: format!("{}/device.crt", path),
            },
        }
    }
}

#[derive(Debug, Deserialize)]
struct ClientTLSConfig {
    key: String,
    cert: String,
}

#[derive(Debug, Deserialize)]
pub(crate) struct LinkConfig {
    pub(crate) method: ConfigMethod,
    #[serde(default)]
    pub(crate) tls_off: bool,
    pub(crate) tls: Option<TLSConfig>,
}

impl Default for LinkConfig {
    fn default() -> Self {
        LinkConfig {
            method: ConfigMethod::Router {
                addr: "https://api.energy.cloud/ecpp/route/device".to_string(),
            },
            tls_off: false,
            tls: None,
        }
    }
}
#[derive(Debug, Deserialize)]
// #[serde(tag = "method")]
pub enum ConfigMethod {
    Fix(Paras),
    Router { addr: String },
}

#[derive(Debug, Clone, Deserialize)]
pub struct Paras {
    pub domain: String,
    pub port: u16,
    pub publish: String,
    pub subscribe: String,
    #[serde(default)]
    pub auth :Option<Authorization>,
}

impl ConfigMethod {
    async fn get_connect_paras(&self) -> anyhow::Result<Paras> {
        match self {
            ConfigMethod::Fix(params) => Ok(params.clone()),
            ConfigMethod::Router { addr } => {
                let cli = reqwest::Client::builder()
                    .danger_accept_invalid_certs(true)
                    .build()?;
                let resp = cli.get(addr).send().await?;
                #[derive(Deserialize)]
                struct Topics {
                    #[serde(alias = "PubTopic")]
                    pub_topic: String,
                    #[serde(alias = "SubTopic")]
                    sub_topic: String,
                }
                #[derive(Deserialize)]
                struct LOT {
                    domain: String,
                    port: String,
                    ext: Topics,
                }
                let bt = resp.bytes().await?;
                let mut value: serde_json::Value =
                    serde_json::from_str(String::from_utf8(bt.to_vec())?.as_ref())?;
                let lot = value
                    .get_mut("data")
                    .ok_or(anyhow!("data not found"))?
                    .get_mut("routes")
                    .ok_or(anyhow!("routes not found"))?
                    .get_mut("iot")
                    .ok_or(anyhow!("iot not found"))?
                    .to_owned();
                let para: LOT = serde_json::from_value(lot)?;
                Ok(Paras {
                    domain: para.domain,
                    port: para.port.trim().parse()?,
                    publish: para.ext.pub_topic,
                    subscribe: para.ext.sub_topic,
                    auth: None,
                })
            }
        }
    }
}

pub trait Linkage {
    fn ready(&self) -> bool;
    async fn login(&self, timeout: Duration) -> anyhow::Result<()>;
    async fn reconnect(&self) -> anyhow::Result<()>;
    async fn send_frame(&self, frame: Frame) -> anyhow::Result<()>;
    async fn recv_frame(&self) -> anyhow::Result<Frame>;
    async fn try_disconnect(&self) -> anyhow::Result<()>;
}

impl MQTT {
    pub(super) fn new(id: &str, codec: Arc<Codec>) -> Self {
        MQTT {
            id: id.to_string(),
            event: Mutex::new(None),
            sender: Mutex::new(None),
            link_config: LinkConfig::default(),
            login: AtomicBool::new(false),
            codec,
        }
    }

    pub(crate) fn set_config(mut self, cfg: LinkConfig) -> Self {
        self.link_config = cfg;
        self
    }

    fn load_tls(&self) -> anyhow::Result<Option<rumqttc::TlsConfiguration>> {
        if let Some(cfg) = &self.link_config.tls {
            let mut ca = Vec::new();
            File::open(&cfg.ca)?.read_to_end(&mut ca)?;
            let mut client_cert = Vec::new();
            File::open(&cfg.client.cert)?.read_to_end(&mut client_cert)?;
            let mut client_key = Vec::new();
            File::open(&cfg.client.key)?.read_to_end(&mut client_key)?;
            Ok(Some(rumqttc::TlsConfiguration::Simple {
                ca,
                alpn: None,
                client_auth: Some((client_cert, client_key)),
            }))
        } else {
            Ok(None)
        }
    }

    async fn wait_for_connected(
        &self,
        tx: AsyncClient,
        mut rx: EventLoop,
    ) -> anyhow::Result<(AsyncClient, EventLoop)> {
        loop {
            let event = rx.poll().await?;
            if let Event::Incoming(inc) = event {
                match inc {
                    Incoming::ConnAck(_) => {
                        info!("cloud mqtt connect ok!");
                        return Ok((tx, rx));
                    }
                    _ => {}
                }
            }
        }
    }

    async fn subscribe(
        &self,
        tx: AsyncClient,
        mut rx: EventLoop,
        paras: &Paras,
        timeout: Duration,
    ) -> anyhow::Result<(AsyncClient, EventLoop)> {
        let subs = TOPICS
            .iter()
            .map(|&e| {
                SubscribeFilter::new(
                    paras
                        .subscribe
                        .replace("${Topic}", e)
                        .replace("${DeviceID}", &self.id)
                        .to_string(),
                    QoS::AtMostOnce,
                )
            })
            .collect::<Vec<_>>();
        _ = tx.subscribe_many(subs).await?;
        let deadline = Instant::now() + timeout;
        loop {
            tokio::select! {
                _ = tokio::time::sleep_until(deadline) => {
                    return Err(anyhow!("subscribe timeout"));
                }
                event = rx.poll() => {
                    let e = event?;
                    if let Event::Incoming(inc) = e {
                        match inc {
                            Incoming::SubAck(_) => {
                                debug!("topics subscribe ok!",);
                                return Ok((tx, rx));
                            }
                            _ => {}
                        }
                    }
                }
            }
        }
    }
}

impl Linkage for MQTT {
    fn ready(&self) -> bool {
        self.login.load(Relaxed)
    }

    async fn login(&self, timeout: Duration) -> anyhow::Result<()> {
        let (lat, lon) = system::network::get_position().unwrap_or((0_f64, 0_f64));
        let req = LoginReq {
            sn: self.id.clone(),
            mac: system::network::get_wlan_mac().unwrap_or("".to_string()),
            lat,
            lon,
            hardware_version: "".to_string(),
            software_version: system::get_os_soft_version().map_or("".to_string(), |e| e.0),
        };
        info!("send login request...");
        self.send_frame(self.codec.encode(&req)).await?;
        let deadline = Instant::now() + timeout;
        debug!(
            "wait login response... {:?}",
            deadline.duration_since(Instant::now()).as_secs()
        );
        loop {
            tokio::select! {
                _ = tokio::time::sleep_until(deadline) => {
                    return Err(anyhow!("login timeout"));
                }
                event = self.recv_frame() =>{
                    match event {
                        Ok(frame) => {
                            if frame.msg_type() != MsgType::LoginAns {
                                warn!("recv message {:?} before login",frame.msg_type());
                            } else {
                                let ans = LoginAns::decode(&*frame.payload)?;
                                return if ans.result() == LoginSuccess {
                                    self.login.store(true,Relaxed);
                                    Ok(())
                                } else {
                                    warn!("login failed due to {:?}",ans.result());
                                    Err(anyhow!(ans.result().as_str_name()))
                                }
                            }
                        }
                        Err(err) => {
                            return Err(err.into());
                        }
                    }
                }
            }
        }
    }

    async fn reconnect(&self) -> anyhow::Result<()> {
        let tls_cfg = self.load_tls().with_context(|| "load tls failed")?;
        info!("fetch connect paras...");
        let paras = self.link_config.method.get_connect_paras().await?;
        info!("fetch connect paras ok,paras = {:?}", paras);
        let mut opts = MqttOptions::new(sn_get()?.to_string(), &paras.domain, paras.port);
        if let Some(cfg) = tls_cfg {
            info!("load tls configs ok");
            opts.set_transport(Transport::Tls(cfg));
        }
        if let Some(auth) = paras.auth.as_ref() {
            opts.set_credentials(&auth.username, &auth.password);
        }
        let (tx, mut rx) = AsyncClient::new(opts, 16);
        rx.network_options.set_connection_timeout(10);
        let (tx, rx) = self.wait_for_connected(tx, rx).await?;
        let (tx, rx) = self
            .subscribe(tx, rx, &paras, Duration::from_secs(10))
            .await?;

        *self.event.lock().await = Some(rx);
        *self.sender.lock().await = Some((
            tx,
            TOPICS
                .iter()
                .map(|&e| {
                    (
                        Topic::from(e),
                        paras.publish.replace("${Topic}", e).to_string(),
                    )
                })
                .collect(),
        ));
        Ok(())
    }

    async fn send_frame(&self, frame: Frame) -> anyhow::Result<()> {
        let sender_opt = self.sender.lock().await;
        if let Some((sender, pubs)) = sender_opt.as_ref() {
            let msg_type: MsgType = crate::cloud::proto::mgcops::MsgType::try_from(frame.msg_type)?;
            let group = msg_type.group();
            if let Some(topic) = pubs.get(&group) {
                sender
                    .publish(topic, QoS::AtMostOnce, false, frame.encode_to_vec())
                    .await?;
                Ok(())
            } else {
                //todo
                Ok(())
            }
        } else {
            Err(anyhow!("not connected"))
        }
    }

    async fn recv_frame(&self) -> anyhow::Result<Frame> {
        let mut event_opt = self.event.lock().await;
        let event = event_opt.as_mut().unwrap();
        loop {
            match event.poll().await {
                Ok(event) => {
                    if let Event::Incoming(Incoming::Publish(msg)) = event {
                        return Ok(Frame::decode(msg.payload)?);
                    }
                }
                Err(e) => {
                    self.login.store(false, Relaxed);
                    return Err(e.into());
                }
            }
        }
    }

    async fn try_disconnect(&self) -> anyhow::Result<()> {
        let sender_opt = self.sender.lock().await;
        if let Some(sender) = sender_opt.as_ref() {
            sender.0.try_disconnect()?;
        }
        Ok(())
    }
}
