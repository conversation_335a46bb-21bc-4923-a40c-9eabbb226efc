/*
 * Copyright (c) 2024. DERI. Nanjing, China. All rights reserved
 * FILENAME: codec.rs
 * PROJECT: opsd
 * AUTHOR: shenshuangquan
 */

use std::fmt::Debug;
use std::time::{SystemTime, UNIX_EPOCH};
use tokio::runtime::Handle;

use crate::cloud::proto::mgcops::Frame;
use crate::cloud::proto::MessageType;
pub(super) struct Codec {
    pub(super) device_id: String,
}

impl Codec {
    pub(super) fn decode<T: prost::Message + Debug + Default>(frame: &Frame) -> anyhow::Result<T> {
        let msg = T::decode(&*frame.payload)?;
        log::debug!(
            "recv message[{}]: {:?}",
            Handle::current().metrics().num_alive_tasks(),
            msg
        );
        Ok(msg)
    }

    pub(super) fn encode<T: prost::Message + MessageType + Debug, F: AsRef<T>>(
        &self,
        msg: F,
    ) -> Frame {
        let msg = msg.as_ref();
        log::debug!(
            "send message[{}]: {:?}",
            Handle::current().metrics().num_alive_tasks(),
            msg
        );
        Frame {
            msg_type: msg.msg_type().into(),
            send_time: SystemTime::now()
                .duration_since(UNIX_EPOCH)
                .unwrap()
                .as_millis() as i64,
            device_id: self.device_id.clone(),
            payload: msg.encode_to_vec(),
            packet_num: 1,
            packet_count: 1,
            ..Default::default()
        }
    }
}
