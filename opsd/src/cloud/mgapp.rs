use std::{
    collections::HashMap,
    io::{BufRead as _, BufReader},
    time::Duration,
};

use crate::cloud::{mgapp::utils::v2::mgcver_programs, *};
use crate::cloud::{
    mgapp::utils::COMMBASE_VERSIONS_URL,
    proto::{
        mgcops::{Device, RemoteControlReq, RtDataAns},
        ProcessStatus,
    },
};

pub enum ProcessOrNone<'a> {
    Process(&'a sysinfo::Process),
    None(String),
}

impl<'a> Into<RtDataAns> for ProcessOrNone<'a> {
    fn into(self) -> RtDataAns {
        let mut r: RtDataAns = match self {
            ProcessOrNone::Process(p) => {
                use sysinfo::ProcessStatus::*;
                let st: u8 = match p.status() {
                    Idle | Run | Sleep => ProcessStatus::Running,
                    Stop => ProcessStatus::Stopped,
                    _ => ProcessStatus::Abnormal,
                }
                .into();
                let mut ans: RtDataAns = [
                    ("cpu_usage", serde_json::json!(p.cpu_usage())),
                    ("mem_used", serde_json::json!(p.memory() / (1024 * 1024))),
                    ("status", serde_json::Value::from(st)),
                    ("start_time", serde_json::json!(p.start_time())),
                ]
                .as_ref()
                .into();
                ans.index = p.name().to_string_lossy().to_string();
                ans
            }
            ProcessOrNone::None(name) => {
                let mut ans: RtDataAns = [(
                    "status",
                    serde_json::Value::from(ProcessStatus::Stopped as u8),
                )]
                .as_ref()
                .into();
                ans.index = name;
                ans
            }
        };
        r.dev_type = "Program".to_string();
        r
    }
}

#[derive(Default)]
pub(crate) struct MGApp {
    programs: HashMap<String, Program>,
    model: Option<Box<dyn ModelProvider + Send + Sync>>,
}

#[derive(Default, Debug)]
pub struct Platform {
    master: String,
    libdrp: String,
    commbase: String,
}

enum ModelVersion {
    V1,
    V2,
}
trait ModelProvider {
    fn get_programs(&self) -> anyhow::Result<Vec<Program>>;
    fn get_running_plugins(&self, name: &str) -> anyhow::Result<Vec<Plugin>>;
    fn version(&self) -> ModelVersion;
}

struct V1ProgramModel {}

impl ModelProvider for V1ProgramModel {
    fn get_programs(&self) -> anyhow::Result<Vec<Program>> {
        let slaves = utils::get_slaves()?;
        Ok(slaves
            .into_iter()
            .map(|e| {
                let sha256 = match std::process::Command::new("sha256sum")
                    .arg(format!("/deri/mgc/{}", e))
                    .output()
                {
                    Ok(output) => String::from_utf8_lossy(&output.stdout)
                        .split_whitespace()
                        .next()
                        .unwrap_or_default()
                        .to_string(),
                    Err(_) => "".to_string(),
                };
                Program {
                    name: e.clone(),
                    version: None,
                    sha256: sha256,
                }
            })
            .collect())
    }

    fn get_running_plugins(&self, name: &str) -> anyhow::Result<Vec<Plugin>> {
        if name.eq("comm") {
            utils::v1::get_running_plugins()
        } else {
            Ok(vec![])
        }
    }

    fn version(&self) -> ModelVersion {
        ModelVersion::V1
    }
}

struct V2ProgramModel {}

impl ModelProvider for V2ProgramModel {
    fn get_programs(&self) -> anyhow::Result<Vec<Program>> {
        let slaves = utils::get_slaves()?;
        let ver_programs: HashMap<String, String> = mgcver_programs()?.into_iter().collect();
        Ok(slaves
            .into_iter()
            .map(|e| {
                let ver =
                    ver_programs.get(&e).cloned().or_else(|| {
                        match std::process::Command::new(format!("/deri/mgc/{}/{}", e, e))
                            .arg("version")
                            .current_dir("/deri/mgc")
                            .output()
                        {
                            Ok(output) => String::from_utf8_lossy(&output.stdout)
                                .split_whitespace()
                                .next()
                                .map(|s| s.to_string()),
                            Err(_) => None,
                        }
                    });
                let sha256 = match std::process::Command::new("sha256sum")
                    .arg(format!("/deri/mgc/{}/{}", e, e))
                    .output()
                {
                    Ok(output) => String::from_utf8_lossy(&output.stdout)
                        .split_whitespace()
                        .next()
                        .unwrap_or_default()
                        .to_string(),
                    Err(_) => "".to_string(),
                };
                Program {
                    name: e.clone(),
                    version: ver,
                    sha256: sha256,
                }
            })
            .collect())
    }

    fn get_running_plugins(&self, name: &str) -> anyhow::Result<Vec<Plugin>> {
        if name.eq("cptplugin") {
            utils::v2::get_running_plugins()
        } else {
            Ok(vec![])
        }
    }

    fn version(&self) -> ModelVersion {
        ModelVersion::V2
    }
}

impl MGApp {
    pub fn new() -> Self {
        let mut s = Self {
            ..Default::default()
        };
        _ = s.reload();
        s
    }

    pub fn get_slaves(&self) -> anyhow::Result<Vec<&Program>> {
        Ok(self.programs.values().collect())
    }

    pub async fn waiting_master(pid: Option<i32>) -> Option<i32> {
        loop {
            match pid {
                Some(id) => {
                    if unsafe { libc::kill(id, 0) != 0 } {
                        break None;
                    } else {
                        tokio::time::sleep(Duration::from_secs(1)).await;
                        continue;
                    }
                }
                None => {
                    let p = std::fs::read_to_string("/var/run/mgc-master.pid")
                        .ok()
                        .map(|e| e.parse::<i32>().ok())
                        .flatten()
                        .and_then(|p| {
                            if unsafe { libc::kill(p, 0) == 0 } {
                                Some(p)
                            } else {
                                None
                            }
                        });
                    if p.is_some() {
                        break p;
                    } else {
                        tokio::time::sleep(Duration::from_secs(10)).await;
                        continue;
                    }
                }
            }
        }
    }

    pub(crate) async fn restart() -> anyhow::Result<()> {
        Self::stop().await?;
        Self::start().await
    }

    pub(crate) async fn stop() -> anyhow::Result<()> {
        let r = tokio::process::Command::new("mgcstop").status().await?;
        if r.success() {
            Ok(())
        } else {
            Err(anyhow::anyhow!("mgcstop failed"))
        }
    }

    pub(crate) async fn start() -> anyhow::Result<()> {
        if tokio::process::Command::new("mgcstart")
            .process_group(0)
            .status()
            .await?
            .success()
        {
            Ok(())
        } else {
            Err(anyhow::anyhow!("mgcstart failed"))
        }
    }
}

impl MGApp {
    pub async fn device(&self) -> anyhow::Result<Vec<Device>> {
        let mut slaves = vec![];
        for ele in self.get_slaves()? {
            let children = match &self.model {
                Some(m) => m
                    .get_running_plugins(&ele.name)?
                    .into_iter()
                    .map(|e| e.into())
                    .collect(),
                None => {
                    vec![]
                }
            };
            let mut device: Device = ele.clone().into();
            device.children = children;
            slaves.push(device);
        }

        let plat = utils::get_platform().await.unwrap_or_default();

        let mut version = utils::get_master_version_by_opkg();
        if version.is_none() {
            match self.model.as_ref() {
                Some(m) => match m.version() {
                    ModelVersion::V1 => {
                        version = utils::get_version_by_sha(&plat.commbase, COMMBASE_VERSIONS_URL)
                            .await
                            .ok();
                    }
                    ModelVersion::V2 => {
                        version = Some(utils::get_master_version_by_slaves(&slaves));
                    }
                },
                None => {}
            }
        }

        Ok(vec![Device {
            dev_type: "Program".to_string(),
            index: "master".to_string(),
            version: version.unwrap_or_default(),
            model: "master".to_string(),
            vendor: "teld".to_string(),
            r#virtual: false,
            children: slaves,
            props: HashMap::from([
                ("master_sha256".to_string(), plat.master),
                ("libdrp_sha256".to_string(), plat.libdrp),
                ("commbase_sha256".to_string(), plat.commbase),
            ]),
            ..Default::default()
        }])
    }

    pub async fn control(&self, req: &RemoteControlReq) -> anyhow::Result<()> {
        tokio::time::timeout(Duration::from_secs(5), async move {
            match req.index.as_str() {
                "master" => {
                    let cmd = req.cmd.as_ref().unwrap();
                    match cmd.cmd_type.as_str() {
                        "Start" => MGApp::start().await,
                        "Stop" => MGApp::stop().await,
                        "Restart" => MGApp::restart().await,
                        _ => Err(anyhow::anyhow!("unsupport")),
                    }
                }
                _ => Err(anyhow::anyhow!("unsupport")),
            }
        })
        .await??;
        Ok(())
    }

    pub fn reload(&mut self) -> anyhow::Result<()> {
        self.model = Some(if utils::is_v2()? {
            Box::new(V2ProgramModel {})
        } else {
            Box::new(V1ProgramModel {})
        });

        let model = self.model.as_ref().unwrap();
        let programs = model
            .get_programs()?
            .into_iter()
            .map(|e| (e.name.clone(), e))
            .collect::<HashMap<_, _>>();
        self.programs.clear();
        for ele in utils::get_slaves()? {
            self.programs.insert(
                ele.clone(),
                if let Some(p) = programs.get(ele.as_str()) {
                    p.clone()
                } else {
                    Program::new(&ele)
                },
            );
        }

        Ok(())
    }

    pub async fn programs(&self) -> anyhow::Result<Vec<&str>> {
        let mut programs = vec!["master"];
        programs.extend(self.programs.keys().map(|e| e.as_str()));
        Ok(programs)
    }
}

mod utils {
    use regex::Regex;

    use super::*;
    pub(super) fn is_v2() -> anyhow::Result<bool> {
        let output = std::process::Command::new("ldd")
            .arg("/deri/mgc/libdrp.so")
            .output()?;
        if output.status.success() {
            Ok(!String::from_utf8_lossy(&output.stdout).contains("boost"))
        } else {
            Err(anyhow::anyhow!("ldd failed"))
        }
    }

    pub(super) fn get_master_version_by_opkg() -> Option<String> {
        let output = std::process::Command::new("opkg")
            .arg("list-installed")
            .output()
            .ok()?;
        if output.status.success() {
            for line in String::from_utf8_lossy(&output.stdout).lines() {
                if line.starts_with("mgc") {
                    return Some(
                        line.split("-")
                            .nth(1)
                            .unwrap_or_default()
                            .trim()
                            .to_string(),
                    );
                }
            }
        }
        None
    }

    #[allow(unused)]
    pub const MASTER_VERSIONS_URL: &str =
        "https://cdn.energy.cloud/file-server/download/mgc/supports/master-versions";
    pub const COMMBASE_VERSIONS_URL: &str =
        "https://cdn.energy.cloud/file-server/download/mgc/supports/commbase-versions";

    pub(super) async fn get_version_by_sha(sha: &str, url: &str) -> anyhow::Result<String> {
        let resp = reqwest::Client::builder()
            .danger_accept_invalid_certs(true)
            .timeout(std::time::Duration::from_secs(5)) // 跳过 SSL 校验
            .build()?
            .get(url)
            .send()
            .await?;
        if !resp.status().is_success() {
            return Err(anyhow::anyhow!("request failed"));
        }
        Ok(resp
            .text()
            .await?
            .lines()
            .find_map(|s| {
                let mut splits = s.split_whitespace();
                if splits.next().unwrap_or_default() == sha {
                    Some(splits.next().unwrap_or_default())
                } else {
                    None
                }
            })
            .unwrap_or_default()
            .to_string())
    }

    pub(super) async fn get_platform() -> anyhow::Result<Platform> {
        let output = tokio::process::Command::new("sha256sum")
            .arg("/deri/mgc/master")
            .arg("/deri/mgc/libdrp.so")
            .arg("/deri/mgc/libcommbase.so")
            .output()
            .await?;
        let mut ret = Platform::default();
        for line in String::from_utf8_lossy(&output.stdout).lines() {
            let splits: Vec<_> = line.split_whitespace().collect();
            if splits.len() > 1 {
                match splits[1] {
                    "/deri/mgc/master" => {
                        ret.master = splits[0].to_string();
                    }
                    "/deri/mgc/libdrp.so" => {
                        ret.libdrp = splits[0].to_string();
                    }
                    "/deri/mgc/libcommbase.so" => {
                        ret.commbase = splits[0].to_string();
                    }
                    _ => {}
                }
            }
        }
        Ok(ret)
    }

    pub(super) fn get_master_version_by_slaves(devices: &Vec<Device>) -> String {
        let mut version_cnt = HashMap::new();
        for ele in devices {
            *version_cnt
                .entry(
                    ele.version
                        .as_str()
                        .trim_start_matches("v")
                        .trim_start_matches("V")
                        .split("_")
                        .next()
                        .unwrap_or_default()
                        .to_string(),
                )
                .or_insert(0) += 1;
        }
        let mut max_version = 0;
        let mut max_version_name = "".to_string();
        for (k, v) in version_cnt {
            if v > max_version {
                max_version = v;
                max_version_name = k;
            }
        }
        max_version_name
    }

    pub(super) fn get_slaves() -> anyhow::Result<Vec<String>> {
        let mut ret = vec![];
        let file = std::fs::File::open("/deri/mgc/config.lua")?;
        let reader = BufReader::new(file);
        for line in reader.lines() {
            match line {
                Ok(line) => {
                    let trim = line.trim();
                    if trim.starts_with("--") {
                        continue;
                    }
                    if trim.starts_with("slaves") {
                        let r = Regex::new(r#"slaves\s*=\s*\{([^}]*)\}"#).unwrap();
                        if let Some(cap) = r.captures(trim) {
                            let matched = cap.get(1);
                            if let Some(v) = matched {
                                let space_removed = v.as_str().trim();
                                for p in space_removed.split(",") {
                                    let p = p.trim().trim_matches('"');
                                    if !p.is_empty() {
                                        ret.push(p.to_string());
                                    }
                                }
                            }
                        }
                    }
                }
                Err(e) => println!("Error reading line: {}", e),
            }
        }
        Ok(ret)
    }

    pub(super) mod v2 {
        use std::collections::HashSet;

        use super::*;
        pub(crate) fn mgcver_programs() -> anyhow::Result<Vec<(String, String)>> {
            let mut ret = vec![];
            let output = std::process::Command::new("mgcver").arg("-p").output()?;
            if output.status.success() {
                for ele in String::from_utf8_lossy(&output.stdout).lines() {
                    let mut splits = ele.split_whitespace();
                    let name = splits.next();
                    let version = splits.next();
                    if name.is_some() && version.is_some() {
                        ret.push((
                            name.unwrap().trim().to_string(),
                            version.unwrap().trim().to_string(),
                        ));
                    }
                }
            }
            Ok(ret)
        }

        pub(crate) fn mgcver_plugins() -> anyhow::Result<Vec<Plugin>> {
            let mut ret = vec![];
            let output = std::process::Command::new("mgcver").arg("-s").output()?;
            if output.status.success() {
                for ele in String::from_utf8_lossy(&output.stdout).lines() {
                    let mut splits = ele.split_whitespace();
                    let name = splits.next();
                    let version = splits.next();
                    if name.is_some() && version.is_some() {
                        ret.push(Plugin {
                            name: name.unwrap().trim().to_string(),
                            version: version.unwrap().trim().to_string(),
                            sha256: "".to_string(),
                        });
                    }
                }
            }
            Ok(ret)
        }

        pub(crate) fn get_running_plugins() -> anyhow::Result<Vec<Plugin>> {
            let mut ret = vec![];
            let output = std::process::Command::new("pidof")
                .arg("cptplugin")
                .output()?;
            if !output.status.success() {
                return Ok(ret);
            }
            let pid = String::from_utf8_lossy(&output.stdout).trim().to_string();
            let plugins = mgcver_plugins()?
                .into_iter()
                .map(|e| (e.name, e.version))
                .collect::<HashMap<_, _>>();
            let mut libnames = HashSet::new();
            for ele in std::fs::read_to_string(format!("/proc/{}/maps", pid.trim()))?.lines() {
                match ele.split_whitespace().nth(5) {
                    Some(s) => {
                        if s.starts_with("/deri/mgc/cptplugin/plugins/") {
                            let libname = std::path::Path::new(s)
                                .file_name()
                                .unwrap()
                                .to_string_lossy();
                            if libnames.contains(libname.as_ref()) {
                                continue;
                            } else {
                                libnames.insert(libname.to_string());
                            }
                            let sha256 =
                                match std::process::Command::new("sha256sum").arg(s).output() {
                                    Ok(output) => String::from_utf8_lossy(&output.stdout)
                                        .split_whitespace()
                                        .next()
                                        .unwrap_or_default()
                                        .to_string(),
                                    Err(_) => "".to_string(),
                                };
                            match plugins.get(libname.as_ref()) {
                                Some(version) => {
                                    ret.push(Plugin {
                                        name: libname.to_string(),
                                        version: version.to_string(),
                                        sha256,
                                    });
                                }
                                None => {
                                    ret.push(Plugin {
                                        name: libname.to_string(),
                                        version: "unknown".to_string(),
                                        sha256,
                                    });
                                }
                            }
                        }
                    }
                    None => {}
                }
            }
            Ok(ret)
        }
    }

    pub(super) mod v1 {
        use super::*;
        use procfs::process::MMapPath;
        pub fn get_running_plugins() -> anyhow::Result<Vec<Plugin>> {
            let output = std::process::Command::new("pidof").arg("comm").output()?;
            if !output.status.success() {
                return Ok(vec![]);
            }
            let pid: i32 = String::from_utf8_lossy(&output.stdout).trim().parse()?;
            let p = procfs::process::Process::new(pid)?;
            let ret = p
                .maps()?
                .into_iter()
                .filter_map(|e| {
                    if let MMapPath::Path(p) = e.pathname {
                        if p.starts_with("/deri/mgc/comm_plugins") {
                            let sha256 = match std::process::Command::new("sha256sum")
                                .arg(p.clone())
                                .output()
                            {
                                Ok(output) => String::from_utf8_lossy(&output.stdout)
                                    .split_whitespace()
                                    .next()
                                    .unwrap_or_default()
                                    .to_string(),
                                Err(_) => "".to_string(),
                            };
                            Some(Plugin {
                                name: p
                                    .to_str()
                                    .unwrap()
                                    .trim_start_matches("/deri/mgc/comm_plugins/")
                                    .to_string(),
                                version: "".to_string(),
                                sha256: sha256,
                            })
                        } else {
                            None
                        }
                    } else {
                        None
                    }
                })
                .collect();
            Ok(ret)
        }
    }
}
