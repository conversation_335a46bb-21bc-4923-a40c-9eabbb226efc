/*
 * Copyright (c) 2024. DERI. Nanjing, China. All rights reserved
 * FILENAME: app.rs
 * PROJECT: opsd
 * AUTHOR: shenshuangquan
 */

use std::fs;
use std::os::unix::fs::PermissionsExt;
use std::path::Path;
use std::process::Stdio;

use anyhow::anyhow;

use crate::app::AppState;

pub const DEFAULT_EXECUTABLE_DIR: &str = "/usr/local/bin";
const APP_MANAGER: &str = "app";

#[allow(unused)]
pub fn set_as_executable(path: &Path) -> anyhow::Result<()> {
    let mut perms = path.metadata().unwrap().permissions();
    perms.set_mode(0o755);
    fs::set_permissions(path, perms)?;
    Ok(())
}

#[derive(Default)]
pub struct AppCommand {}

impl crate::app::AppManager for AppCommand {
    fn list(&self) -> anyhow::Result<Vec<AppState>> {
        log::info!("list app");
        let status = std::process::Command::new("app").arg("list").output()?;
        if status.status.success() {
            let mut apps: Vec<AppState> = vec![];
            let result = String::from_utf8_lossy(&status.stdout).to_string();
            let lines = result.lines();
            let first_lines = lines.take(1);
            let mut name_idx: Option<usize> = None;
            let mut stage_idx: Option<usize> = None;
            let mut status_idx: Option<usize> = None;
            let mut running_idx = None;
            let mut columns = 3;
            for x in first_lines {
                let str_vec: Vec<&str> = x.trim().split_whitespace().collect();
                columns = str_vec.len();
                for (idx, ele) in str_vec.iter().enumerate() {
                    match ele.to_lowercase().as_str() {
                        "appname" => {
                            name_idx = Some(idx);
                        }
                        "stage" => {
                            stage_idx = Some(idx);
                        }
                        "status" => {
                            status_idx = Some(idx);
                        }
                        "running" => {
                            running_idx = Some(idx);
                        }
                        _ => {}
                    }
                }
            }
            result.lines().skip(1).for_each(|e| {
                let str_vec: Vec<&str> = e.trim().split_whitespace().collect();
                if str_vec.len() == columns {
                    apps.push(AppState {
                        name: if name_idx.is_none() {
                            "".to_string()
                        } else {
                            str_vec[name_idx.unwrap()].to_string()
                        },
                        stage: if stage_idx.is_none() {
                            "".to_string()
                        } else {
                            str_vec[stage_idx.unwrap()].to_string()
                        },
                        running: if running_idx.is_none() {
                            false
                        } else if str_vec[name_idx.unwrap()].eq("True") {
                            true
                        } else {
                            false
                        },
                        enabled: if status_idx.is_none() {
                            false
                        } else if str_vec[name_idx.unwrap()].eq("enabled") {
                            true
                        } else {
                            false
                        },
                    });
                }
            });
            Ok(apps)
        } else {
            Err(anyhow!(String::from_utf8_lossy(&status.stderr).to_string()))
        }
    }

    fn start(&self, name: &str) -> anyhow::Result<()> {
        log::info!("start app: {}", name);
        let status = std::process::Command::new(APP_MANAGER)
            .stderr(Stdio::null())
            .stdout(Stdio::null())
            .arg("start")
            .arg(name)
            .output()?;
        if status.status.success() {
            Ok(())
        } else {
            Err(anyhow!(String::from_utf8_lossy(&status.stderr).to_string()))
        }
    }

    fn stop(&self, name: &str) -> anyhow::Result<()> {
        log::info!("stop app: {}", name);
        let status = std::process::Command::new(APP_MANAGER)
            .stderr(Stdio::null())
            .stdout(Stdio::null())
            .arg("stop")
            .arg(name)
            .output()?;
        if status.status.success() {
            Ok(())
        } else {
            Err(anyhow!(String::from_utf8_lossy(&status.stderr).to_string()))
        }
    }

    fn status(&self, name: &str) -> anyhow::Result<Option<crate::app::ProcStat>> {
        let reg = crate::app::builtin::find_app_registry(name)?;
        Ok(crate::app::monit::PROCESS_MONITOR.get_process(reg.process_name()))
    }

    fn version(&self, name: &str) -> anyhow::Result<String> {
        let reg = crate::app::builtin::find_app_registry(name)?;
        crate::app::builtin::get_version(reg.process_name())
    }
}
