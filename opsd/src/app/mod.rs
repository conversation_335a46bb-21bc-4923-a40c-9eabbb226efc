mod builtin;

pub use builtin::AppToolBuiltin as AppToolBuiltin;

pub trait AppManager {
    fn list(&self) -> anyhow::Result<Vec<Box<dyn Application + Sync + Send>>>;
    #[allow(unused)]
    fn get(&self, name: &str) -> Option<Box<dyn Application + Sync + Send>>;
    #[allow(unused)]
    fn enable(&self, name: &str) -> anyhow::Result<()>;
    #[allow(unused)]
    fn disable(&self, name: &str) -> anyhow::Result<()>;
}

pub trait Application {
    fn name(&self) -> &str;
    fn version(&self) -> anyhow::Result<(String, String)>;
    fn start(&self) -> anyhow::Result<()>;
    fn stop(&self) -> anyhow::Result<()>;
    fn restart(&self) -> anyhow::Result<()> {
        self.stop()?;
        self.start()
    }
}