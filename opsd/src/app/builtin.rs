use anyhow::anyhow;
use std::io;
use std::path::Path;
use std::path::PathBuf;
use std::process::Output;
use std::process::Stdio;
use std::thread;
use std::time::Duration;
use std::time::Instant;

use crate::app::AppManager;
use crate::app::Application;
const FILE_PATH: &str = "/usr/lib/app-init.d";
const LINK_PATH: &str = "/etc/app-init.d";

pub fn get_possible_paths() -> anyhow::Result<Vec<PathBuf>> {
    Ok(std::fs::read_dir(FILE_PATH)?
        .into_iter()
        .filter_map(|e| {
            if let Ok(entry) = e {
                if entry.file_type().is_ok_and(|f| f.is_file())
                    && entry
                        .path()
                        .file_stem()
                        .is_some_and(|e| e.to_string_lossy().starts_with("S"))
                {
                    Some(entry.path())
                } else {
                    None
                }
            } else {
                None
            }
        })
        .collect())
}

pub fn find_app_registry(name: &str) -> anyhow::Result<AppRegistry> {
    get_possible_paths()?
        .into_iter()
        .find_map(|e| {
            let reg_name = e.file_name().unwrap().to_string_lossy();
            if reg_name.ends_with(name) {
                AppRegistry::new(e).ok()
            } else {
                None
            }
        })
        .ok_or(anyhow!("app not found"))
}

pub struct AppRegistry {
    path: PathBuf,
    #[allow(unused)]
    level: i32,
    name: String,
    daemon: Option<String>,
    #[allow(unused)]
    enabled: bool,
    #[allow(unused)]
    pidfile: Option<std::path::PathBuf>,
}

impl AppRegistry {
    pub fn get_registered_path<P: AsRef<Path>>(path: P) -> anyhow::Result<Option<PathBuf>> {
        for ele in std::fs::read_dir(LINK_PATH)?.into_iter() {
            if let Ok(e) = ele {
                if e.file_type()?.is_symlink() {
                    let link_path = e.path().read_link()?;
                    if link_path.eq(path.as_ref()) {
                        return Ok(Some(e.path()));
                    }
                }
            }
        }
        Ok(None)
    }

    pub fn process_name(&self) -> &str {
        self.daemon.as_ref().unwrap_or(&self.name)
    }

    pub fn new<P: AsRef<Path>>(path: P) -> anyhow::Result<Self> {
        let p = path.as_ref();
        let pp = p
            .file_stem()
            .ok_or(anyhow!("invalid app name"))?
            .to_string_lossy();
        let re = regex::Regex::new(r"^S(\d+)(.*)").unwrap();
        let caps = re.captures(&pp).ok_or(anyhow!("invalid app name"))?;
        let daemon_re = regex::Regex::new(r"DAEMON=(\S+)")?;
        let mut app = AppRegistry {
            path: p.to_path_buf(),
            level: caps.get(1).unwrap().as_str().parse::<i32>()?,
            name: caps.get(2).unwrap().as_str().to_string(),
            daemon: None,
            enabled: Self::get_registered_path(p)?.is_some(),
            pidfile: None,
        };
        let content = std::fs::read_to_string(p)?;
        match daemon_re.captures(content.as_str()) {
            Some(daemon_caps) => {
                app.daemon = Some(daemon_caps.get(1).unwrap().as_str().to_string());
            }
            None => {}
        }
        Ok(app)
    }

    #[allow(unused)]
    pub fn process(&self) -> anyhow::Result<Option<psutil::process::Process>> {
        if self.pidfile.is_some() {
            let pid = std::fs::read_to_string(self.pidfile.as_ref().unwrap())?;
            if let Ok(pid) = pid.trim().parse::<u32>() {
                return Ok(Some(psutil::process::Process::new(pid)?));
            }
        }
        let name = self.daemon.as_ref().unwrap_or(&self.name);
        match psutil::process::processes()?.into_iter().find(|e| {
            e.as_ref()
                .is_ok_and(|p| p.name().unwrap_or_default().eq(name))
        }) {
            Some(p) => Ok(Some(p?)),
            None => Ok(None),
        }
    }
}

pub fn get_version_by_opkg(name:&str) -> Option<String> {
        let output = std::process::Command::new("opkg")
            .arg("list-installed")
            .output()
            .ok()?;
        if output.status.success() {
            for line in String::from_utf8_lossy(&output.stdout).lines() {
                let mut splits = line.split("-");
                if splits.next().unwrap_or_default().trim() == name {
                    return Some(splits.next().unwrap_or_default().trim().to_string());
                }
            }
        }
        None
}

pub fn get_version(name: &str) -> anyhow::Result<(String, String)> {
    if let Some(version) = get_version_by_opkg(name) {
        return Ok((version, "".to_string()));
    }
    let timeout = Duration::from_secs(5);
    let mut bin_path = std::env::var("PATH")?;
    if !bin_path.contains("/usr/local/bin") {
        bin_path = format!("{}:/usr/local/bin", bin_path);
    }
    let sha256_output = std::process::Command::new("sha256sum")
        .arg(format!("/usr/local/bin/{}", name))
        .output()?;
    let sha256 = String::from_utf8_lossy(&sha256_output.stdout)
        .split_whitespace()
        .next()
        .unwrap_or_default()
        .to_string();
    let mut child = std::process::Command::new(name)
        .env("PATH", bin_path)
        .arg("--version")
        .stdout(std::process::Stdio::piped())
        .stderr(std::process::Stdio::piped())
        .spawn()?;
    let start_time = Instant::now();
    loop {
        if start_time.elapsed() > timeout {
            child.kill()?;
            return Err(io::Error::new(io::ErrorKind::TimedOut, "Process timed out").into());
        }
        match child.try_wait()? {
            None => {
                thread::sleep(Duration::from_millis(100));
            }
            Some(status) => {
                return Ok((
                    if status.success() {
                        let output: Output = child.wait_with_output()?;
                        let mut version_str = String::from_utf8_lossy(&output.stdout).to_string();
                        if version_str.is_empty() {
                            version_str = String::from_utf8_lossy(&output.stderr).to_string();
                        }
                        let splits: Vec<&str> = version_str.trim().split_whitespace().collect();
                        if splits.len() > 1 {
                            splits[1].to_string()
                        } else if splits.len() > 0 {
                            splits[0].to_string()
                        } else {
                            "unknown".to_string()
                        }
                    } else {
                        "unknown".to_string()
                    },
                    sha256,
                ))
            }
        }
    }
}

pub struct AppToolBuiltin {}

impl AppManager for AppToolBuiltin {
    fn list(&self) -> anyhow::Result<Vec<Box<dyn Application + Sync + Send>>> {
        Ok(get_possible_paths()?
            .into_iter()
            .filter_map(|e| match AppRegistry::new(e) {
                Ok(app) => Some(Box::new(app) as Box<dyn Application + Sync + Send>),
                Err(_) => None,
            })
            .collect())
    }

    fn get(&self, name: &str) -> Option<Box<dyn Application + Sync + Send>> {
        find_app_registry(name)
            .ok()
            .map(|e| Box::new(e) as Box<dyn Application + Sync + Send>)
    }

    fn enable(&self, name: &str) -> anyhow::Result<()> {
        let path = find_app_registry(name)?.path;
        let filename = path.as_path().file_name().unwrap().to_string_lossy();
        let link_path = format!("{}/{}", LINK_PATH, filename);
        if !std::path::Path::new(&link_path).exists() {
            std::os::unix::fs::symlink(path.as_path(), format!("{}/{}", LINK_PATH, filename))?;
        }
        Ok(())
    }

    fn disable(&self, name: &str) -> anyhow::Result<()> {
        let path = find_app_registry(name)?.path;
        let filename = path.as_path().file_name().unwrap().to_string_lossy();
        let link_path = format!("{}/{}", LINK_PATH, filename);
        std::fs::remove_file(link_path.as_str())?;
        Ok(())
    }
}

impl Application for AppRegistry {
    fn name(&self) -> &str {
        &self.name
    }

    fn version(&self) -> anyhow::Result<(String, String)> {
        get_version(self.process_name())
    }

    fn start(&self) -> anyhow::Result<()> {
        log::debug!("run {} start", self.path.display());
        let output = std::process::Command::new(self.path.as_path())
            .arg("start")
            .output()?;
        if !output.status.success() {
            log::error!(
                "run {} start failed, err: {}",
                self.path.display(),
                String::from_utf8_lossy(&output.stderr)
            );
            return Err(anyhow!("start failed"));
        }
        let process = self.process_name();
        let timeout_duration = Duration::from_secs(5);
        let interval_duration = Duration::from_millis(100);
        let start_time = Instant::now();
        loop {
            if start_time.elapsed() >= timeout_duration {
                return Err(anyhow!("timeout"));
            }
            thread::sleep(interval_duration);
            let out = std::process::Command::new("pidof")
                .args(["-x", process])
                .output()?;
            if out.status.success() {
                log::debug!(
                    "run {} start ok, pid: {}",
                    self.path.display(),
                    String::from_utf8_lossy(&out.stdout)
                );
                return Ok(());
            }
        }
    }

    fn stop(&self) -> anyhow::Result<()> {
        let stat = std::process::Command::new(self.path.as_path())
            .arg("stop")
            .stdout(Stdio::null())
            .status()?;
        if !stat.success() {
            return Err(anyhow!("stop fail"));
        }
        Ok(())
    }

    fn restart(&self) -> anyhow::Result<()> {
        self.stop()?;
        self.start()
    }
}
