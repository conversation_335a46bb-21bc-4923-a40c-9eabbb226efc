{"version": "0.2.0", "configurations": [{"type": "lldb", "request": "launch", "name": "Debug unit tests in library 'opsd'", "cargo": {"args": ["test", "--no-run", "--bin", "--package=opsd"], "filter": {"name": "opsd", "kind": "bin"}}, "args": [], "cwd": "${workspaceFolder}"}, {"type": "lldb", "request": "launch", "name": "Debug executable 'opsd'", "cargo": {"args": ["build", "--bin=opsd", "--package=opsd"], "filter": {"name": "opsd", "kind": "bin"}}, "args": [], "cwd": "${workspaceFolder}"}, {"type": "lldb", "request": "launch", "name": "Debug unit tests in executable 'drmd'", "cargo": {"args": ["test", "--no-run", "--bin=opsd", "--package=opsd"], "filter": {"name": "drmd", "kind": "bin"}}, "args": [], "cwd": "${workspaceFolder}"}]}