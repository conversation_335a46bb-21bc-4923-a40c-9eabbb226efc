#!/usr/bin/env bash
function generate_checksum_file() {
  current_dir=${PWD}
  cd "$1" || return 1
  echo "#generate automatically by pack.sh,do not edit it" >"$2"
  while IFS= read -r -d '' file; do
    hash=$(sha256sum "$file")
    echo "sha256 ${hash}" >>"$2"
  done < <(find . -type f ! -name "*.hash" -print0)
  cd "${current_dir}" || return 1
}

TMP_DIR=./pack

function pack_ipk() {
  rm -rf ${TMP_DIR}
  mkdir -p ${TMP_DIR}/CONTROL
  mkdir -p ${TMP_DIR}/usr/lib/app-init.d
  mkdir -p ${TMP_DIR}/usr/local/bin/
  opsd_version=$(grep -r "^version" ./opsd/Cargo.toml | sed 's/version = "\(\S*\)"/\1/' | sed 's/[[:space:]]*$//')
  {
    echo "Package: opsd"
    echo "Version: "$opsd_version
    echo "Section: Applications"
    echo "Priority: optional"
    echo "Architecture: arm"
    echo "Depends: "
    echo "Maintainer: <EMAIL>"
    echo "Description: microgrid controller operate application."
  } >>${TMP_DIR}/CONTROL/control

  cp ./opsd/S05opsd ${TMP_DIR}/usr/lib/app-init.d/
  cp ./target/armv7-unknown-linux-gnueabihf/release/opsd ${TMP_DIR}/usr/local/bin/
  /opt/opkg-utils/opkg-build ${TMP_DIR}
  #cleanup
  rm -rf ./pack
}

function pack_dpkg() {
  rm -rf ${TMP_DIR}
  mkdir -p ${TMP_DIR}
  #install
  mkdir ${TMP_DIR}/init/
  cp ./opsd/S05opsd ${TMP_DIR}/init/
  mkdir ${TMP_DIR}/bin/
  cp ./target/armv7-unknown-linux-gnueabihf/release/opsd ${TMP_DIR}/bin/
  #checksum
  generate_checksum_file ./pack ./opsd.hash
  #pack
  opsd_version=$(grep -r "^version" ./opsd/Cargo.toml | sed 's/version = "\(\S*\)"/\1/' | sed 's/[[:space:]]*$//')
  cd ./pack && zip -r ../opsd-"${opsd_version}"-arm.zip ./* && cd ..
  #cleanup
}

case $1 in
"opsd")
  pack_ipk
  pack_dpkg
  ;;
*)
  pack_ipk
  pack_dpkg
  ;;
esac
