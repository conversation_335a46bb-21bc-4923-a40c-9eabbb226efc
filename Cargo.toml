[workspace]
members = ["rtm", "opsd", "ops-cellular"]
resolver = "2"
[workspace.dependencies]
tokio = { version = "1", features = ["full"] }
rumqttc = { version = "0.24" }
serde_json = "1.0"
serde = { version = "1.0", features = ["derive"] }
anyhow = "1.0.79"
protobuf = "3.3.0"
quote = "1.0.33"
syn = "2.0.39"
log = "0.4.20"
chrono = "0.4.33"
reqwest = "0.11.24"
base64 = "0.21.5"
trait-variant = "0.1.2"
env_logger = "0.11.3"
sysinfo = { version = "0.30" }
sha2 = { version = "0.10" }
regex = "1.10.2"
