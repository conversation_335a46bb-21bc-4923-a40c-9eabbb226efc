TMPDIR ?= /tmp/opsd-build
OPSD_EXE=target/armv7-unknown-linux-gnueabihf/release/opsd
OPSD_VERSION=$(shell grep '^version =' ./opsd/Cargo.toml | sed -E 's/version = "(.*)"/\1/')
prefix=.

define CONTROL_FILE
@echo "Package: opsd"
@echo "Version: $(OPSD_VERSION)"
@echo "Section: Applications"
@echo "Architecture: arm"
@echo "Maintainer: shenshuangquan <<EMAIL>>"
@echo "Description: Microgrid Operation Support Platform"
@echo "Homepage: https://gitee.com/njderi/opsd"
@echo "Priority: optional"
endef

ipk: opsd
	rm -rf $(TMPDIR) && mkdir -p $(TMPDIR)
	cd $(TMPDIR) && mkdir -p CONTROL usr/local/bin usr/lib/app-init.d etc/logrotate.d
	$(CONTROL_FILE) > $(TMPDIR)/CONTROL/control
	install -Dm644 ./opsd/opsd.conf $(TMPDIR)/etc/logrotate.d/
	install -Dm755 $(OPSD_EXE) $(TMPDIR)/usr/local/bin/opsd
	install -Dm755 ./opsd/S05opsd $(TMPDIR)/usr/lib/app-init.d/
# 	rm -rf $(TMPDIR)

.PHONY: opsd
opsd:
	cargo build --target=armv7-unknown-linux-gnueabihf --release -p opsd