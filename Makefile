TMPDIR ?= /tmp/opsd-build
OPSD_EXE=target/armv7-unknown-linux-gnueabihf/release/opsd
OPSD_VERSION=$(shell grep '^version =' ./opsd/Cargo.toml | sed -E 's/version = "(.*)"/\1/')
CONTROL_DIR ?= $(TMPDIR)/CONTROL
CONTROL_FILE_PATH ?= $(CONTROL_DIR)/control
prefix=.

# Package information variables (can be overridden)
PACKAGE_NAME ?= opsd
PACKAGE_SECTION ?= Applications
PACKAGE_ARCH ?= arm
PACKAGE_MAINTAINER ?= shenshuangquan <<EMAIL>>
PACKAGE_DESCRIPTION ?= Microgrid Operation Support Platform
PACKAGE_HOMEPAGE ?= https://gitee.com/njderi/opsd
PACKAGE_PRIORITY ?= optional

# Generate control file content
define CONTROL_CONTENT
Package: $(PACKAGE_NAME)
Version: $(OPSD_VERSION)
Section: $(PACKAGE_SECTION)
Architecture: $(PACKAGE_ARCH)
Maintainer: $(PACKAGE_MAINTAINER)
Description: $(PACKAGE_DESCRIPTION)
Homepage: $(PACKAGE_HOMEPAGE)
Priority: $(PACKAGE_PRIORITY)
endef

# Generate control file
.PHONY: control
control:
	@echo "Generating control file..."
	@mkdir -p $(CONTROL_DIR)
	@echo "$$CONTROL_CONTENT" > $(CONTROL_FILE_PATH)
	@echo "Control file generated at: $(CONTROL_FILE_PATH)"
	@echo "Content:"
	@cat $(CONTROL_FILE_PATH)

# Generate control file to a specific location
control-to:
	@if [ -z "$(OUTPUT)" ]; then \
		echo "Error: OUTPUT variable must be specified"; \
		echo "Usage: make control-to OUTPUT=/path/to/control"; \
		exit 1; \
	fi
	@echo "Generating control file to $(OUTPUT)..."
	@mkdir -p $$(dirname $(OUTPUT))
	@echo "$$CONTROL_CONTENT" > $(OUTPUT)
	@echo "Control file generated at: $(OUTPUT)"

ipk: opsd control
	rm -rf $(TMPDIR) && mkdir -p $(TMPDIR)
	cd $(TMPDIR) && mkdir -p CONTROL usr/local/bin usr/lib/app-init.d etc/logrotate.d
	@echo "$$CONTROL_CONTENT" > $(TMPDIR)/CONTROL/control
	install -Dm644 ./opsd/opsd.conf $(TMPDIR)/etc/logrotate.d/
	install -Dm755 $(OPSD_EXE) $(TMPDIR)/usr/local/bin/opsd
	install -Dm755 ./opsd/S05opsd $(TMPDIR)/usr/lib/app-init.d/
# 	rm -rf $(TMPDIR)

# Show package version
.PHONY: version
version:
	@echo "Package: $(PACKAGE_NAME)"
	@echo "Version: $(OPSD_VERSION)"

# Show control file content without generating it
.PHONY: show-control
show-control:
	@echo "Control file content:"
	@echo "$$CONTROL_CONTENT"

# Clean build artifacts and temporary files
.PHONY: clean
clean:
	cargo clean
	rm -rf $(TMPDIR)
	@echo "Cleaned build artifacts and temporary files"

# Clean only control files
.PHONY: clean-control
clean-control:
	rm -rf $(CONTROL_DIR)
	@echo "Cleaned control files"

.PHONY: opsd
opsd:
	cargo build --target=armv7-unknown-linux-gnueabihf --release -p opsd

# Help target
.PHONY: help
help:
	@echo "Available targets:"
	@echo "  opsd          - Build the opsd binary"
	@echo "  control       - Generate control file in $(CONTROL_FILE_PATH)"
	@echo "  control-to    - Generate control file to specific location (use OUTPUT=path)"
	@echo "  show-control  - Show control file content without generating"
	@echo "  version       - Show package name and version"
	@echo "  ipk           - Build IPK package (includes opsd and control)"
	@echo "  clean         - Clean all build artifacts and temporary files"
	@echo "  clean-control - Clean only control files"
	@echo "  help          - Show this help message"
	@echo ""
	@echo "Variables that can be overridden:"
	@echo "  PACKAGE_NAME        = $(PACKAGE_NAME)"
	@echo "  PACKAGE_SECTION     = $(PACKAGE_SECTION)"
	@echo "  PACKAGE_ARCH        = $(PACKAGE_ARCH)"
	@echo "  PACKAGE_MAINTAINER  = $(PACKAGE_MAINTAINER)"
	@echo "  PACKAGE_DESCRIPTION = $(PACKAGE_DESCRIPTION)"
	@echo "  PACKAGE_HOMEPAGE    = $(PACKAGE_HOMEPAGE)"
	@echo "  PACKAGE_PRIORITY    = $(PACKAGE_PRIORITY)"
	@echo "  TMPDIR              = $(TMPDIR)"
	@echo "  CONTROL_DIR         = $(CONTROL_DIR)"