TMPDIR ?= /tmp/opsd-build
OPSD_EXE=target/armv7-unknown-linux-gnueabihf/release/opsd
OPSD_VERSION=$(shell grep '^version =' ./opsd/Cargo.toml | sed -E 's/version = "(.*)"/\1/')
CONTROL_DIR ?= $(TMPDIR)/CONTROL
CONTROL_FILE_PATH ?= $(CONTROL_DIR)/control
prefix=.

# Package information variables (can be overridden)
PACKAGE_NAME ?= opsd
PACKAGE_SECTION ?= Applications
PACKAGE_ARCH ?= arm
PACKAGE_MAINTAINER ?= shenshuangquan <<EMAIL>>
PACKAGE_DESCRIPTION ?= Microgrid Operation Support Platform
PACKAGE_HOMEPAGE ?= https://gitee.com/njderi/opsd
PACKAGE_PRIORITY ?= optional

ipk: opsd
	rm -rf $(TMPDIR) && mkdir -p $(TMPDIR)
	cd $(TMPDIR) && mkdir -p CONTROL usr/local/bin usr/lib/app-init.d etc/logrotate.d
	@echo "Package: $(PACKAGE_NAME)" > $(TMPDIR)/CONTROL/control
	@echo "Version: $(OPSD_VERSION)" >> $(TMPDIR)/CONTROL/control
	@echo "Section: $(PACKAGE_SECTION)" >> $(TMPDIR)/CONTROL/control
	@echo "Architecture: $(PACKAGE_ARCH)" >> $(TMPDIR)/CONTROL/control
	@echo "Maintainer: $(PACKAGE_MAINTAINER)" >> $(TMPDIR)/CONTROL/control
	@echo "Description: $(PACKAGE_DESCRIPTION)" >> $(TMPDIR)/CONTROL/control
	@echo "Homepage: $(PACKAGE_HOMEPAGE)" >> $(TMPDIR)/CONTROL/control
	@echo "Priority: $(PACKAGE_PRIORITY)" >> $(TMPDIR)/CONTROL/control
	install -Dm644 ./opsd/opsd.conf $(TMPDIR)/etc/logrotate.d/
	install -Dm755 $(OPSD_EXE) $(TMPDIR)/usr/local/bin/opsd
	install -Dm755 ./opsd/S05opsd $(TMPDIR)/usr/lib/app-init.d/
# 	rm -rf $(TMPDIR)

# Show package version
.PHONY: version
version:
	@echo "Package: $(PACKAGE_NAME)"
	@echo "Version: $(OPSD_VERSION)"

# Show control file content without generating it
.PHONY: show-control
show-control:
	@echo "Control file content:"
	@echo "Package: $(PACKAGE_NAME)"
	@echo "Version: $(OPSD_VERSION)"
	@echo "Section: $(PACKAGE_SECTION)"
	@echo "Architecture: $(PACKAGE_ARCH)"
	@echo "Maintainer: $(PACKAGE_MAINTAINER)"
	@echo "Description: $(PACKAGE_DESCRIPTION)"
	@echo "Homepage: $(PACKAGE_HOMEPAGE)"
	@echo "Priority: $(PACKAGE_PRIORITY)"

# Clean build artifacts and temporary files
.PHONY: clean
clean:
	cargo clean
	rm -rf $(TMPDIR)
	@echo "Cleaned build artifacts and temporary files"

.PHONY: opsd
opsd:
	cargo build --target=armv7-unknown-linux-gnueabihf --release -p opsd

# Help target
.PHONY: help
help:
	@echo "Available targets:"
	@echo "  opsd          - Build the opsd binary"
	@echo "  show-control  - Show control file content without generating"
	@echo "  version       - Show package name and version"
	@echo "  ipk           - Build IPK package (includes opsd and control)"
	@echo "  clean         - Clean all build artifacts and temporary files"
	@echo "  help          - Show this help message"
	@echo ""
	@echo "Variables that can be overridden:"
	@echo "  PACKAGE_NAME        = $(PACKAGE_NAME)"
	@echo "  PACKAGE_SECTION     = $(PACKAGE_SECTION)"
	@echo "  PACKAGE_ARCH        = $(PACKAGE_ARCH)"
	@echo "  PACKAGE_MAINTAINER  = $(PACKAGE_MAINTAINER)"
	@echo "  PACKAGE_DESCRIPTION = $(PACKAGE_DESCRIPTION)"
	@echo "  PACKAGE_HOMEPAGE    = $(PACKAGE_HOMEPAGE)"
	@echo "  PACKAGE_PRIORITY    = $(PACKAGE_PRIORITY)"
	@echo "  TMPDIR              = $(TMPDIR)"
	@echo "  CONTROL_DIR         = $(CONTROL_DIR)"