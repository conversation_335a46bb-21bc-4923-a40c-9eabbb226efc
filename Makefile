TMPDIR ?= /tmp/opsd-build
OPSD_EXE=target/armv7-unknown-linux-gnueabihf/release/opsd
OPSD_VERSION=$(shell grep '^version =' ./opsd/Cargo.toml | sed -E 's/version = "(.*)"/\1/')
CONTROL_DIR ?= $(TMPDIR)/CONTROL
CONTROL_FILE_PATH ?= $(CONTROL_DIR)/control
IPK_FILE ?= $(PACKAGE_NAME)_$(OPSD_VERSION)_$(PACKAGE_ARCH).ipk
ZIP_FILE ?= $(PACKAGE_NAME)-$(OPSD_VERSION)-$(PACKAGE_ARCH).zip
DEPLOY_SCRIPT ?= deploy.sh
prefix=.

# Package information variables (can be overridden)
PACKAGE_NAME ?= opsd
PACKAGE_SECTION ?= Applications
PACKAGE_ARCH ?= arm
PACKAGE_MAINTAINER ?= shenshuangquan <<EMAIL>>
PACKAGE_DESCRIPTION ?= Microgrid Operation Support Platform
PACKAGE_HOMEPAGE ?= https://gitee.com/njderi/opsd
PACKAGE_PRIORITY ?= optional

ipk: opsd
	@echo "Building IPK package..."
	rm -rf $(TMPDIR) && mkdir -p $(TMPDIR)
	cd $(TMPDIR) && mkdir -p CONTROL usr/local/bin usr/lib/app-init.d etc/logrotate.d
	@echo "Package: $(PACKAGE_NAME)" > $(TMPDIR)/CONTROL/control
	@echo "Version: $(OPSD_VERSION)" >> $(TMPDIR)/CONTROL/control
	@echo "Section: $(PACKAGE_SECTION)" >> $(TMPDIR)/CONTROL/control
	@echo "Architecture: $(PACKAGE_ARCH)" >> $(TMPDIR)/CONTROL/control
	@echo "Maintainer: $(PACKAGE_MAINTAINER)" >> $(TMPDIR)/CONTROL/control
	@echo "Description: $(PACKAGE_DESCRIPTION)" >> $(TMPDIR)/CONTROL/control
	@echo "Homepage: $(PACKAGE_HOMEPAGE)" >> $(TMPDIR)/CONTROL/control
	@echo "Priority: $(PACKAGE_PRIORITY)" >> $(TMPDIR)/CONTROL/control
	install -Dm644 ./opsd/opsd.conf $(TMPDIR)/etc/logrotate.d/
	install -Dm755 $(OPSD_EXE) $(TMPDIR)/usr/local/bin/opsd
	install -Dm755 ./opsd/S05opsd $(TMPDIR)/usr/lib/app-init.d/
	@echo "Creating IPK archive..."
	cd $(TMPDIR) && tar czf data.tar.gz -C . --exclude=CONTROL .
	cd $(TMPDIR) && tar czf control.tar.gz -C CONTROL .
	cd $(TMPDIR) && echo "2.0" > debian-binary
	cd $(TMPDIR) && ar r ../$(IPK_FILE) debian-binary control.tar.gz data.tar.gz
	@echo "IPK package created: $(IPK_FILE)"
	rm -rf $(TMPDIR)

# Create ZIP package containing IPK and deploy script
.PHONY: zip
zip: ipk
	@echo "Creating ZIP package..."
	@if [ ! -f "$(IPK_FILE)" ]; then \
		echo "Error: IPK file $(IPK_FILE) not found"; \
		exit 1; \
	fi
	@if [ ! -f "$(DEPLOY_SCRIPT)" ]; then \
		echo "Error: Deploy script $(DEPLOY_SCRIPT) not found"; \
		exit 1; \
	fi
	@rm -f $(ZIP_FILE)
	zip $(ZIP_FILE) $(IPK_FILE) $(DEPLOY_SCRIPT)
	@echo "ZIP package created: $(ZIP_FILE)"
	@echo "Contents:"
	@unzip -l $(ZIP_FILE)

# Create ZIP package with custom files
.PHONY: zip-with
zip-with: ipk
	@if [ -z "$(FILES)" ]; then \
		echo "Error: FILES variable must be specified"; \
		echo "Usage: make zip-with FILES=\"file1 file2 file3\""; \
		exit 1; \
	fi
	@echo "Creating ZIP package with custom files..."
	@if [ ! -f "$(IPK_FILE)" ]; then \
		echo "Error: IPK file $(IPK_FILE) not found"; \
		exit 1; \
	fi
	@rm -f $(ZIP_FILE)
	zip $(ZIP_FILE) $(IPK_FILE) $(FILES)
	@echo "ZIP package created: $(ZIP_FILE)"
	@echo "Contents:"
	@unzip -l $(ZIP_FILE)

# Show package version
.PHONY: version
version:
	@echo "Package: $(PACKAGE_NAME)"
	@echo "Version: $(OPSD_VERSION)"

# Show control file content without generating it
.PHONY: show-control
show-control:
	@echo "Control file content:"
	@echo "Package: $(PACKAGE_NAME)"
	@echo "Version: $(OPSD_VERSION)"
	@echo "Section: $(PACKAGE_SECTION)"
	@echo "Architecture: $(PACKAGE_ARCH)"
	@echo "Maintainer: $(PACKAGE_MAINTAINER)"
	@echo "Description: $(PACKAGE_DESCRIPTION)"
	@echo "Homepage: $(PACKAGE_HOMEPAGE)"
	@echo "Priority: $(PACKAGE_PRIORITY)"

# Clean build artifacts and temporary files
.PHONY: clean
clean:
	cargo clean
	rm -rf $(TMPDIR)
	rm -f *.ipk *.zip
	@echo "Cleaned build artifacts, temporary files, IPK and ZIP packages"

# Clean only packages (IPK and ZIP files)
.PHONY: clean-packages
clean-packages:
	rm -f *.ipk *.zip
	@echo "Cleaned IPK and ZIP packages"

.PHONY: opsd
opsd:
	cargo build --target=armv7-unknown-linux-gnueabihf --release -p opsd

# Help target
.PHONY: help
help:
	@echo "Available targets:"
	@echo "  opsd            - Build the opsd binary"
	@echo "  ipk             - Build IPK package (includes opsd and control)"
	@echo "  zip             - Create ZIP package with IPK and deploy.sh"
	@echo "  zip-with        - Create ZIP package with custom files (use FILES=\"file1 file2\")"
	@echo "  show-control    - Show control file content without generating"
	@echo "  version         - Show package name and version"
	@echo "  clean           - Clean all build artifacts, temporary files, and packages"
	@echo "  clean-packages  - Clean only IPK and ZIP packages"
	@echo "  help            - Show this help message"
	@echo ""
	@echo "Package files:"
	@echo "  IPK file: $(IPK_FILE)"
	@echo "  ZIP file: $(ZIP_FILE)"
	@echo "  Deploy script: $(DEPLOY_SCRIPT)"
	@echo ""
	@echo "Variables that can be overridden:"
	@echo "  PACKAGE_NAME        = $(PACKAGE_NAME)"
	@echo "  PACKAGE_SECTION     = $(PACKAGE_SECTION)"
	@echo "  PACKAGE_ARCH        = $(PACKAGE_ARCH)"
	@echo "  PACKAGE_MAINTAINER  = $(PACKAGE_MAINTAINER)"
	@echo "  PACKAGE_DESCRIPTION = $(PACKAGE_DESCRIPTION)"
	@echo "  PACKAGE_HOMEPAGE    = $(PACKAGE_HOMEPAGE)"
	@echo "  PACKAGE_PRIORITY    = $(PACKAGE_PRIORITY)"
	@echo "  TMPDIR              = $(TMPDIR)"
	@echo "  IPK_FILE            = $(IPK_FILE)"
	@echo "  ZIP_FILE            = $(ZIP_FILE)"
	@echo "  DEPLOY_SCRIPT       = $(DEPLOY_SCRIPT)"